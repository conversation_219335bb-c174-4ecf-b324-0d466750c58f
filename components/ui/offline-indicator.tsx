'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Wifi, WifiOff, RefreshCw, AlertTriangle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { ClientOnly } from '@/components/client-only';

interface OfflineIndicatorProps {
  className?: string;
}

function OfflineIndicatorContent({ className }: OfflineIndicatorProps) {
  const [isOnline, setIsOnline] = useState(true);
  const [isReconnecting, setIsReconnecting] = useState(false);
  const [showIndicator, setShowIndicator] = useState(false);

  useEffect(() => {
    // Set initial online status safely
    const initialOnlineStatus = typeof navigator !== 'undefined' ? navigator.onLine : true;
    setIsOnline(initialOnlineStatus);

    const handleOnline = () => {
      setIsOnline(true);
      setIsReconnecting(false);
      // Hide indicator after a brief success message
      setTimeout(() => setShowIndicator(false), 3000);
    };

    const handleOffline = () => {
      setIsOnline(false);
      setShowIndicator(true);
    };

    // Listen for online/offline events
    if (typeof window !== 'undefined') {
      window.addEventListener('online', handleOnline);
      window.addEventListener('offline', handleOffline);

      // Show indicator if already offline
      if (!navigator.onLine) {
        setShowIndicator(true);
      }
    }

    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
      }
    };
  }, []);

  const handleReconnect = async () => {
    setIsReconnecting(true);
    
    try {
      // Try to fetch a simple endpoint to test connectivity
      const response = await fetch('/api/health', {
        method: 'HEAD',
        cache: 'no-cache'
      });
      
      if (response.ok) {
        setIsOnline(true);
        setShowIndicator(false);
      } else {
        // Still offline
        setTimeout(() => setIsReconnecting(false), 1000);
      }
    } catch (error) {
      // Still offline
      setTimeout(() => setIsReconnecting(false), 1000);
    }
  };

  if (!showIndicator) {
    return null;
  }

  return (
    <div className={cn(
      "fixed bottom-4 right-4 z-50 max-w-sm",
      className
    )}>
      <Alert className={cn(
        "border-2 shadow-lg",
        isOnline
          ? "border-green-500 bg-green-50 text-green-800 dark:bg-green-950 dark:text-green-200"
          : "border-orange-500 bg-orange-50 text-orange-800 dark:bg-orange-950 dark:text-orange-200"
      )}>
        <div className="flex items-center space-x-2">
          {isOnline ? (
            <Wifi className="h-4 w-4 text-green-600" />
          ) : (
            <WifiOff className="h-4 w-4 text-orange-600" />
          )}
          <AlertDescription className="flex-1">
            {isOnline ? (
              <span className="font-medium">Back online!</span>
            ) : (
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="h-4 w-4" />
                  <span className="font-medium">You're offline</span>
                </div>
                <p className="text-sm">
                  Some features may not work. Check your internet connection.
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleReconnect}
                  disabled={isReconnecting}
                  className="w-full mt-2"
                >
                  {isReconnecting ? (
                    <>
                      <RefreshCw className="h-3 w-3 mr-2 animate-spin" />
                      Reconnecting...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="h-3 w-3 mr-2" />
                      Try Again
                    </>
                  )}
                </Button>
              </div>
            )}
          </AlertDescription>
        </div>
      </Alert>
    </div>
  );
}

export function OfflineIndicator({ className }: OfflineIndicatorProps) {
  return (
    <ClientOnly fallback={null}>
      <OfflineIndicatorContent className={className} />
    </ClientOnly>
  );
}
