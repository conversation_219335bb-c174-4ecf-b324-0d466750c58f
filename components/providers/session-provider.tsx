'use client';

import { SessionProvider } from 'next-auth/react';
import { ReactNode, useState, useEffect } from 'react';

interface Props {
  children: ReactNode;
}

export function AuthProvider({ children }: Props) {
  const [mounted, setMounted] = useState(false);

  // Prevent hydration mismatch in Next.js 15
  useEffect(() => {
    setMounted(true);
  }, []);

  // Return children without SessionProvider during SSR to prevent hydration issues
  if (!mounted) {
    return <div suppressHydrationWarning>{children}</div>;
  }

  return (
    <SessionProvider
      // Reduce session polling to prevent unnecessary requests
      refetchInterval={5 * 60} // 5 minutes
      refetchOnWindowFocus={true}
      // Ensure proper session handling in Next.js 15
      basePath="/api/auth"
    >
      {children}
    </SessionProvider>
  );
}
