'use client';

import { SessionProvider } from 'next-auth/react';
import { ReactNode, useState, useEffect } from 'react';

interface Props {
  children: ReactNode;
}

export function AuthProvider({ children }: Props) {
  return (
    <SessionProvider
      // Reduce session polling to prevent unnecessary requests
      refetchInterval={5 * 60} // 5 minutes
      refetchOnWindowFocus={true}
      // Ensure session is available during SSR
      session={null}
    >
      {children}
    </SessionProvider>
  );
}
