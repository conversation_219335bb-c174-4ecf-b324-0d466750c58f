'use client';

import { SessionProvider } from 'next-auth/react';
import { ThemeProvider } from 'next-themes';
import { ReactNode, useEffect, useState } from 'react';

interface AppProvidersProps {
  children: ReactNode;
}

export function AppProviders({ children }: AppProvidersProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    console.log('AppProviders: useEffect triggered');
    setMounted(true);
  }, []);

  console.log('AppProviders: Ren<PERSON> called, mounted:', mounted);

  // Always render with providers, but handle the SessionProvider error gracefully
  return (
    <SessionProvider
      refetchInterval={5 * 60}
      refetchOnWindowFocus={true}
      basePath="/api/auth"
    >
      <ThemeProvider
        attribute="class"
        defaultTheme="system"
        enableSystem
        disableTransitionOnChange
        storageKey="theme"
        enableColorScheme={false}
      >
        {children}
      </ThemeProvider>
    </SessionProvider>
  );
}
