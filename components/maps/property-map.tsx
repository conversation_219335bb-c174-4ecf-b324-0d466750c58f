'use client';

import { useState, useEffect, useRef } from 'react';
import { Property } from '@/types';

interface PropertyMapProps {
  properties: Property[];
  center?: { lat: number; lng: number };
  zoom?: number;
  height?: string;
  onPropertySelect?: (property: Property) => void;
  selectedProperty?: Property;
  showTransitStops?: boolean;
  showZoning?: boolean;
}

export function PropertyMap({
  properties,
  center,
  zoom = 12,
  height = '400px',
  onPropertySelect,
  selectedProperty,
  showTransitStops = false,
  showZoning = false,
}: PropertyMapProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [markers, setMarkers] = useState<google.maps.Marker[]>([]);
  const [infoWindow, setInfoWindow] = useState<google.maps.InfoWindow | null>(null);

  // Initialize map
  useEffect(() => {
    if (!mapRef.current || map) return;

    const defaultCenter = center || { lat: 34.0522, lng: -118.2437 }; // Los Angeles

    const newMap = new google.maps.Map(mapRef.current, {
      center: defaultCenter,
      zoom,
      styles: [
        {
          featureType: 'poi',
          elementType: 'labels',
          stylers: [{ visibility: 'off' }],
        },
      ],
    });

    const newInfoWindow = new google.maps.InfoWindow();

    setMap(newMap);
    setInfoWindow(newInfoWindow);
  }, [center, zoom, map]);

  // Update markers when properties change
  useEffect(() => {
    if (!map || !infoWindow) return;

    // Clear existing markers
    markers.forEach(marker => marker.setMap(null));

    // Create new markers
    const newMarkers = properties.map(property => {
      const marker = new google.maps.Marker({
        position: property.coordinates,
        map,
        title: property.address,
        icon: {
          url: getMarkerIcon(property),
          scaledSize: new google.maps.Size(40, 40),
        },
      });

      marker.addListener('click', () => {
        const content = createInfoWindowContent(property);
        infoWindow.setContent(content);
        infoWindow.open(map, marker);
        
        if (onPropertySelect) {
          onPropertySelect(property);
        }
      });

      return marker;
    });

    setMarkers(newMarkers);

    // Fit map to show all properties
    if (properties.length > 0) {
      const bounds = new google.maps.LatLngBounds();
      properties.forEach(property => {
        bounds.extend(property.coordinates);
      });
      map.fitBounds(bounds);
    }
  }, [map, properties, infoWindow, onPropertySelect]);

  // Highlight selected property
  useEffect(() => {
    if (!selectedProperty || !map) return;

    const marker = markers.find(m => 
      m.getTitle() === selectedProperty.address
    );

    if (marker) {
      map.setCenter(selectedProperty.coordinates);
      map.setZoom(16);
      
      const content = createInfoWindowContent(selectedProperty);
      infoWindow?.setContent(content);
      infoWindow?.open(map, marker);
    }
  }, [selectedProperty, map, markers, infoWindow]);

  // Add transit stops overlay
  useEffect(() => {
    if (!map || !showTransitStops) return;

    // This would integrate with transit APIs to show nearby stops
    // For now, we'll show mock transit stops
    const transitStops = [
      { lat: 34.0522, lng: -118.2437, name: 'Metro Red Line - Union Station' },
      { lat: 34.0574, lng: -118.2362, name: 'Metro Purple Line - Civic Center' },
    ];

    const transitMarkers = transitStops.map(stop => {
      return new google.maps.Marker({
        position: stop,
        map,
        title: stop.name,
        icon: {
          url: '/icons/transit-stop.png',
          scaledSize: new google.maps.Size(24, 24),
        },
      });
    });

    return () => {
      transitMarkers.forEach(marker => marker.setMap(null));
    };
  }, [map, showTransitStops]);

  // Add zoning overlay
  useEffect(() => {
    if (!map || !showZoning) return;

    // This would integrate with zoning APIs to show zoning boundaries
    // For now, we'll show a sample zoning overlay
    const zoningOverlay = new google.maps.Polygon({
      paths: [
        { lat: 34.050, lng: -118.250 },
        { lat: 34.055, lng: -118.250 },
        { lat: 34.055, lng: -118.240 },
        { lat: 34.050, lng: -118.240 },
      ],
      strokeColor: '#FF0000',
      strokeOpacity: 0.8,
      strokeWeight: 2,
      fillColor: '#FF0000',
      fillOpacity: 0.1,
    });

    zoningOverlay.setMap(map);

    return () => {
      zoningOverlay.setMap(null);
    };
  }, [map, showZoning]);

  const getMarkerIcon = (property: Property): string => {
    // Determine marker color based on evaluation results
    if (property.evaluationResults) {
      if (property.evaluationResults.passed) {
        return '/icons/marker-green.png';
      } else {
        return '/icons/marker-red.png';
      }
    }
    return '/icons/marker-blue.png';
  };

  const createInfoWindowContent = (property: Property): string => {
    const formatCurrency = (amount: number) => {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
      }).format(amount);
    };

    const formatNumber = (num: number) => {
      return new Intl.NumberFormat('en-US').format(num);
    };

    let evaluationBadge = '';
    if (property.evaluationResults) {
      const badgeClass = property.evaluationResults.passed 
        ? 'background-color: #10b981; color: white;' 
        : 'background-color: #ef4444; color: white;';
      
      evaluationBadge = `
        <div style="margin-bottom: 8px;">
          <span style="padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; ${badgeClass}">
            ${property.evaluationResults.passed ? 'QUALIFIED' : 'NOT QUALIFIED'}
          </span>
          <span style="margin-left: 8px; font-weight: bold;">
            Score: ${property.evaluationResults.overallScore}/100
          </span>
        </div>
      `;
    }

    return `
      <div style="max-width: 300px; padding: 8px;">
        ${evaluationBadge}
        <h3 style="margin: 0 0 8px 0; font-size: 16px; font-weight: bold;">
          ${property.address}
        </h3>
        <div style="margin-bottom: 4px;">
          <strong>Lot Size:</strong> ${formatNumber(property.lotSize)} sq ft
        </div>
        <div style="margin-bottom: 4px;">
          <strong>Zoning:</strong> ${property.zoning}
        </div>
        <div style="margin-bottom: 4px;">
          <strong>Assessed Value:</strong> ${formatCurrency(property.assessedValue)}
        </div>
        <div style="margin-bottom: 8px;">
          <strong>Owner:</strong> ${property.owner.name}
        </div>
        <button 
          onclick="window.location.href='/properties/${property.id}'"
          style="
            background-color: #3b82f6; 
            color: white; 
            border: none; 
            padding: 6px 12px; 
            border-radius: 4px; 
            cursor: pointer;
            font-size: 14px;
          "
        >
          View Details
        </button>
      </div>
    `;
  };

  return (
    <div className="relative">
      <div 
        ref={mapRef} 
        style={{ height, width: '100%' }}
        className="rounded-lg border"
      />
      
      {/* Map Controls */}
      <div className="absolute top-4 right-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-2 space-y-2">
        <label className="flex items-center space-x-2 text-sm">
          <input
            type="checkbox"
            checked={showTransitStops}
            onChange={(e) => {
              // This would be handled by parent component
              console.log('Toggle transit stops:', e.target.checked);
            }}
            className="rounded"
          />
          <span>Transit Stops</span>
        </label>
        
        <label className="flex items-center space-x-2 text-sm">
          <input
            type="checkbox"
            checked={showZoning}
            onChange={(e) => {
              // This would be handled by parent component
              console.log('Toggle zoning:', e.target.checked);
            }}
            className="rounded"
          />
          <span>Zoning</span>
        </label>
      </div>

      {/* Legend */}
      <div className="absolute bottom-4 left-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-3">
        <h4 className="text-sm font-medium mb-2">Legend</h4>
        <div className="space-y-1 text-xs">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span>Qualified Properties</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
            <span>Not Qualified</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
            <span>Not Evaluated</span>
          </div>
        </div>
      </div>
    </div>
  );
}

// Hook to load Google Maps API
export function useGoogleMaps() {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    if (typeof window !== 'undefined' && window.google) {
      setIsLoaded(true);
      return;
    }

    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&libraries=places`;
    script.async = true;
    script.defer = true;
    
    script.onload = () => {
      setIsLoaded(true);
    };

    document.head.appendChild(script);

    return () => {
      document.head.removeChild(script);
    };
  }, []);

  return isLoaded;
}
