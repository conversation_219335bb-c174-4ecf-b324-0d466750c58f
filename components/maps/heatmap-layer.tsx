'use client';

import { useEffect, useRef, useState } from 'react';
import { Property } from '@/types';

export interface HeatmapDataPoint {
  lat: number;
  lng: number;
  weight: number;
  value?: number;
  label?: string;
}

export interface HeatmapLayerProps {
  map: google.maps.Map | null;
  data: HeatmapDataPoint[];
  type: 'property_density' | 'property_values' | 'development_potential' | 'risk_assessment';
  visible: boolean;
  opacity?: number;
  radius?: number;
  gradient?: string[];
  onDataPointClick?: (point: HeatmapDataPoint) => void;
}

export interface HeatmapLegend {
  min: number;
  max: number;
  unit: string;
  colors: string[];
  labels: string[];
}

export default function HeatmapLayer({
  map,
  data,
  type,
  visible,
  opacity = 0.6,
  radius = 20,
  gradient,
  onDataPointClick
}: HeatmapLayerProps) {
  const heatmapRef = useRef<google.maps.visualization.HeatmapLayer | null>(null);
  const [legend, setLegend] = useState<HeatmapLegend | null>(null);

  useEffect(() => {
    if (!map || !window.google?.maps?.visualization) return;

    // Create heatmap layer
    const heatmapData = data.map(point => ({
      location: new google.maps.LatLng(point.lat, point.lng),
      weight: point.weight
    }));

    const heatmap = new google.maps.visualization.HeatmapLayer({
      data: heatmapData,
      map: visible ? map : null,
      radius: radius,
      opacity: opacity,
      gradient: gradient || getDefaultGradient(type)
    });

    heatmapRef.current = heatmap;

    // Generate legend
    const legendData = generateLegend(data, type);
    setLegend(legendData);

    // Add click listeners for data points if callback provided
    if (onDataPointClick) {
      const markers = data.map(point => {
        const marker = new google.maps.Marker({
          position: { lat: point.lat, lng: point.lng },
          map: visible ? map : null,
          icon: {
            path: google.maps.SymbolPath.CIRCLE,
            scale: 0, // Invisible marker for click detection
            fillOpacity: 0,
            strokeOpacity: 0
          }
        });

        marker.addListener('click', () => {
          onDataPointClick(point);
        });

        return marker;
      });

      return () => {
        markers.forEach(marker => marker.setMap(null));
        heatmap.setMap(null);
      };
    }

    return () => {
      heatmap.setMap(null);
    };
  }, [map, data, type, visible, opacity, radius, gradient, onDataPointClick]);

  useEffect(() => {
    if (heatmapRef.current) {
      heatmapRef.current.setMap(visible ? map : null);
    }
  }, [visible, map]);

  return null; // This component doesn't render anything directly
}

/**
 * Generate property density heatmap data
 */
export function generatePropertyDensityHeatmap(properties: Property[]): HeatmapDataPoint[] {
  const gridSize = 0.01; // Approximately 1km grid
  const grid: Map<string, { count: number; totalValue: number; lat: number; lng: number }> = new Map();

  // Aggregate properties into grid cells
  properties.forEach(property => {
    const gridLat = Math.round(property.coordinates.lat / gridSize) * gridSize;
    const gridLng = Math.round(property.coordinates.lng / gridSize) * gridSize;
    const key = `${gridLat}_${gridLng}`;

    if (!grid.has(key)) {
      grid.set(key, { count: 0, totalValue: 0, lat: gridLat, lng: gridLng });
    }

    const cell = grid.get(key)!;
    cell.count += 1;
    cell.totalValue += property.assessedValue;
  });

  // Convert to heatmap data points
  return Array.from(grid.values()).map(cell => ({
    lat: cell.lat,
    lng: cell.lng,
    weight: Math.min(cell.count / 10, 1), // Normalize to 0-1
    value: cell.count,
    label: `${cell.count} properties`
  }));
}

/**
 * Generate property values heatmap data
 */
export function generatePropertyValuesHeatmap(properties: Property[]): HeatmapDataPoint[] {
  const maxValue = Math.max(...properties.map(p => p.assessedValue));
  
  return properties.map(property => ({
    lat: property.coordinates.lat,
    lng: property.coordinates.lng,
    weight: property.assessedValue / maxValue,
    value: property.assessedValue,
    label: `$${property.assessedValue.toLocaleString()}`
  }));
}

/**
 * Generate development potential heatmap data
 */
export function generateDevelopmentPotentialHeatmap(
  properties: Property[],
  evaluations: Map<string, any>
): HeatmapDataPoint[] {
  return properties
    .map(property => {
      const evaluation = evaluations.get(property.id);
      const score = evaluation?.overallScore || 0;
      
      return {
        lat: property.coordinates.lat,
        lng: property.coordinates.lng,
        weight: score / 100,
        value: score,
        label: `Development Score: ${score}/100`
      };
    })
    .filter(point => point.weight > 0);
}

/**
 * Generate risk assessment heatmap data
 */
export function generateRiskAssessmentHeatmap(
  properties: Property[],
  riskAssessments: Map<string, any>
): HeatmapDataPoint[] {
  return properties
    .map(property => {
      const risk = riskAssessments.get(property.id);
      const riskScore = risk?.riskScore || 0;
      
      return {
        lat: property.coordinates.lat,
        lng: property.coordinates.lng,
        weight: riskScore / 100,
        value: riskScore,
        label: `Risk Score: ${riskScore}/100`
      };
    })
    .filter(point => point.weight > 0);
}

/**
 * Get default gradient for heatmap type
 */
function getDefaultGradient(type: HeatmapLayerProps['type']): string[] {
  const gradients = {
    property_density: [
      'rgba(0, 255, 255, 0)',
      'rgba(0, 255, 255, 1)',
      'rgba(0, 191, 255, 1)',
      'rgba(0, 127, 255, 1)',
      'rgba(0, 63, 255, 1)',
      'rgba(0, 0, 255, 1)',
      'rgba(0, 0, 223, 1)',
      'rgba(0, 0, 191, 1)',
      'rgba(0, 0, 159, 1)',
      'rgba(0, 0, 127, 1)',
      'rgba(63, 0, 91, 1)',
      'rgba(127, 0, 63, 1)',
      'rgba(191, 0, 31, 1)',
      'rgba(255, 0, 0, 1)'
    ],
    property_values: [
      'rgba(0, 255, 0, 0)',
      'rgba(255, 255, 0, 1)',
      'rgba(255, 127, 0, 1)',
      'rgba(255, 0, 0, 1)'
    ],
    development_potential: [
      'rgba(255, 0, 0, 0)',
      'rgba(255, 255, 0, 1)',
      'rgba(0, 255, 0, 1)'
    ],
    risk_assessment: [
      'rgba(0, 255, 0, 0)',
      'rgba(255, 255, 0, 1)',
      'rgba(255, 127, 0, 1)',
      'rgba(255, 0, 0, 1)'
    ]
  };

  return gradients[type];
}

/**
 * Generate legend for heatmap
 */
function generateLegend(data: HeatmapDataPoint[], type: HeatmapLayerProps['type']): HeatmapLegend {
  const values = data.map(point => point.value || point.weight).filter(v => v !== undefined);
  const min = Math.min(...values);
  const max = Math.max(...values);

  const legends = {
    property_density: {
      unit: 'properties',
      colors: ['#0000FF', '#FF0000'],
      labels: ['Low Density', 'High Density']
    },
    property_values: {
      unit: '$',
      colors: ['#00FF00', '#FFFF00', '#FF0000'],
      labels: ['Low Value', 'Medium Value', 'High Value']
    },
    development_potential: {
      unit: 'score',
      colors: ['#FF0000', '#FFFF00', '#00FF00'],
      labels: ['Low Potential', 'Medium Potential', 'High Potential']
    },
    risk_assessment: {
      unit: 'risk',
      colors: ['#00FF00', '#FFFF00', '#FF0000'],
      labels: ['Low Risk', 'Medium Risk', 'High Risk']
    }
  };

  const legendConfig = legends[type];

  return {
    min,
    max,
    unit: legendConfig.unit,
    colors: legendConfig.colors,
    labels: legendConfig.labels
  };
}

/**
 * Heatmap Legend Component
 */
export function HeatmapLegendComponent({ legend, type }: { legend: HeatmapLegend; type: string }) {
  if (!legend) return null;

  return (
    <div className="absolute bottom-4 left-4 bg-white p-3 rounded-lg shadow-lg border">
      <h4 className="font-semibold text-sm mb-2 capitalize">
        {type.replace('_', ' ')} Heatmap
      </h4>
      
      <div className="flex items-center space-x-2 text-xs">
        <span>{legend.labels[0]}</span>
        
        <div className="flex h-4 w-20 rounded">
          {legend.colors.map((color, index) => (
            <div
              key={index}
              className="flex-1 first:rounded-l last:rounded-r"
              style={{ backgroundColor: color }}
            />
          ))}
        </div>
        
        <span>{legend.labels[legend.labels.length - 1]}</span>
      </div>
      
      <div className="flex justify-between text-xs text-gray-600 mt-1">
        <span>
          {legend.unit === '$' 
            ? `$${legend.min.toLocaleString()}` 
            : `${legend.min.toFixed(0)} ${legend.unit}`
          }
        </span>
        <span>
          {legend.unit === '$' 
            ? `$${legend.max.toLocaleString()}` 
            : `${legend.max.toFixed(0)} ${legend.unit}`
          }
        </span>
      </div>
    </div>
  );
}

/**
 * Heatmap Controls Component
 */
export function HeatmapControls({
  availableTypes,
  selectedType,
  onTypeChange,
  visible,
  onVisibilityChange,
  opacity,
  onOpacityChange
}: {
  availableTypes: Array<{ value: HeatmapLayerProps['type']; label: string }>;
  selectedType: HeatmapLayerProps['type'];
  onTypeChange: (type: HeatmapLayerProps['type']) => void;
  visible: boolean;
  onVisibilityChange: (visible: boolean) => void;
  opacity: number;
  onOpacityChange: (opacity: number) => void;
}) {
  return (
    <div className="absolute top-4 right-4 bg-white p-3 rounded-lg shadow-lg border">
      <h4 className="font-semibold text-sm mb-2">Heatmap Controls</h4>
      
      <div className="space-y-3">
        <div>
          <label className="block text-xs font-medium mb-1">Type</label>
          <select
            value={selectedType}
            onChange={(e) => onTypeChange(e.target.value as HeatmapLayerProps['type'])}
            className="w-full text-xs border rounded px-2 py-1"
          >
            {availableTypes.map(type => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>
        
        <div>
          <label className="flex items-center text-xs">
            <input
              type="checkbox"
              checked={visible}
              onChange={(e) => onVisibilityChange(e.target.checked)}
              className="mr-2"
            />
            Show Heatmap
          </label>
        </div>
        
        {visible && (
          <div>
            <label className="block text-xs font-medium mb-1">
              Opacity: {Math.round(opacity * 100)}%
            </label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={opacity}
              onChange={(e) => onOpacityChange(parseFloat(e.target.value))}
              className="w-full"
            />
          </div>
        )}
      </div>
    </div>
  );
}
