'use client';

import { useTheme } from 'next-themes';
import { But<PERSON> } from '@/components/ui/button';
import { Moon, Sun } from 'lucide-react';
import { ClientOnly } from '@/components/client-only';

export function ThemeToggle() {
  const { theme, setTheme } = useTheme();

  return (
    <ClientOnly
      fallback={
        <Button variant="ghost" size="icon" disabled>
          <Sun className="h-4 w-4" />
          <span className="sr-only">Toggle theme</span>
        </Button>
      }
    >
      <Button
        variant="ghost"
        size="icon"
        onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
      >
        {theme === 'light' ? (
          <Moon className="h-4 w-4" />
        ) : (
          <Sun className="h-4 w-4" />
        )}
        <span className="sr-only">Toggle theme</span>
      </Button>
    </ClientOnly>
  );
}
