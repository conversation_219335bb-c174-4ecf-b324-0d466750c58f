const CACHE_NAME = 'real-estate-automation-v1';
const STATIC_CACHE_NAME = 'static-v1';
const DYNAMIC_CACHE_NAME = 'dynamic-v1';

// Authentication state tracking
let isAuthenticated = false;

// Assets to cache on install
const STATIC_ASSETS = [
  '/',
  '/dashboard',
  '/properties/search',
  '/properties/compare',
  '/properties/map',
  '/reports',
  '/status',
  '/settings',
  '/manifest.json',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png'
];

// API endpoints to cache
const API_CACHE_PATTERNS = [
  /^\/api\/properties\/search/,
  /^\/api\/properties\/evaluate/,
  /^\/api\/reports/,
  /^\/api\/analytics/
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then((cache) => {
        console.log('Service Worker: Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('Service Worker: Static assets cached');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('Service Worker: Error caching static assets', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE_NAME && cacheName !== DYNAMIC_CACHE_NAME) {
              console.log('Service Worker: Deleting old cache', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('Service Worker: Activated');
        return self.clients.claim();
      })
  );
});

// Fetch event - serve from cache or network
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }

  // Skip chrome-extension and other non-http requests
  if (!request.url.startsWith('http')) {
    return;
  }

  // Handle different types of requests
  if (isStaticAsset(request)) {
    event.respondWith(handleStaticAsset(request));
  } else if (isAPIRequest(request)) {
    event.respondWith(handleAPIRequest(request));
  } else if (isNavigationRequest(request)) {
    event.respondWith(handleNavigationRequest(request));
  } else {
    event.respondWith(handleOtherRequest(request));
  }
});

// Check if request is for a static asset
function isStaticAsset(request) {
  const url = new URL(request.url);
  return url.pathname.startsWith('/icons/') ||
         url.pathname.startsWith('/images/') ||
         url.pathname.startsWith('/_next/static/') ||
         url.pathname.endsWith('.js') ||
         url.pathname.endsWith('.css') ||
         url.pathname.endsWith('.png') ||
         url.pathname.endsWith('.jpg') ||
         url.pathname.endsWith('.svg');
}

// Check if request is for API
function isAPIRequest(request) {
  const url = new URL(request.url);
  return url.pathname.startsWith('/api/');
}

// Check if request is for navigation
function isNavigationRequest(request) {
  return request.mode === 'navigate';
}

// Handle static assets - cache first strategy
async function handleStaticAsset(request) {
  try {
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }

    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(STATIC_CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.error('Service Worker: Error handling static asset', error);
    return new Response('Asset not available offline', { status: 503 });
  }
}

// Handle API requests - network first with cache fallback
async function handleAPIRequest(request) {
  const url = new URL(request.url);

  // Check if this is an authenticated endpoint
  const isAuthenticatedEndpoint = url.pathname.startsWith('/api/reports/') ||
                                 url.pathname.startsWith('/api/properties/evaluate') ||
                                 url.pathname.startsWith('/api/auth/session') ||
                                 url.pathname.startsWith('/api/analytics/') ||
                                 url.pathname.includes('protected');

  // Skip caching for authenticated endpoints when not logged in
  if (isAuthenticatedEndpoint && !isAuthenticated) {
    try {
      const networkResponse = await fetch(request);
      // Don't cache responses when not authenticated
      return networkResponse;
    } catch (error) {
      console.error('Service Worker: Network error for authenticated API request', error);
      return new Response(
        JSON.stringify({
          error: 'Authentication required',
          authRequired: true,
          offline: true
        }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
  }

  try {
    // Try network first
    const networkResponse = await fetch(request);

    if (networkResponse.ok) {
      // Cache successful responses for certain endpoints (only when authenticated for protected endpoints)
      if (shouldCacheAPIResponse(request) && (!isAuthenticatedEndpoint || isAuthenticated)) {
        const cache = await caches.open(DYNAMIC_CACHE_NAME);
        cache.put(request, networkResponse.clone());
      }
      return networkResponse;
    }

    // If network fails, try cache (only for non-authenticated endpoints or when authenticated)
    if (!isAuthenticatedEndpoint || isAuthenticated) {
      const cachedResponse = await caches.match(request);
      if (cachedResponse) {
        return cachedResponse;
      }
    }

    return networkResponse;
  } catch (error) {
    console.error('Service Worker: Network error for API request', error);

    // Try to serve from cache (only for non-authenticated endpoints or when authenticated)
    if (!isAuthenticatedEndpoint || isAuthenticated) {
      const cachedResponse = await caches.match(request);
      if (cachedResponse) {
        return cachedResponse;
      }
    }

    // Return appropriate offline response
    const errorResponse = isAuthenticatedEndpoint && !isAuthenticated ? {
      error: 'Authentication required',
      authRequired: true,
      offline: true
    } : {
      error: 'Service unavailable offline',
      offline: true
    };

    return new Response(
      JSON.stringify(errorResponse),
      {
        status: isAuthenticatedEndpoint && !isAuthenticated ? 401 : 503,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

// Handle navigation requests - network first with offline fallback
async function handleNavigationRequest(request) {
  try {
    const networkResponse = await fetch(request);
    return networkResponse;
  } catch (error) {
    console.error('Service Worker: Navigation request failed', error);
    
    // Try to serve cached page
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Serve offline page
    const offlinePage = await caches.match('/');
    return offlinePage || new Response('Offline', { status: 503 });
  }
}

// Handle other requests
async function handleOtherRequest(request) {
  try {
    return await fetch(request);
  } catch (error) {
    const cachedResponse = await caches.match(request);
    return cachedResponse || new Response('Resource not available offline', { status: 503 });
  }
}

// Check if API response should be cached
function shouldCacheAPIResponse(request) {
  const url = new URL(request.url);
  return API_CACHE_PATTERNS.some(pattern => pattern.test(url.pathname));
}

// Background sync for offline actions
self.addEventListener('sync', (event) => {
  console.log('Service Worker: Background sync triggered', event.tag);
  
  if (event.tag === 'property-search') {
    event.waitUntil(syncPropertySearches());
  } else if (event.tag === 'report-generation') {
    event.waitUntil(syncReportGeneration());
  }
});

// Sync offline property searches
async function syncPropertySearches() {
  try {
    console.log('Service Worker: Syncing offline property searches');
    // Implementation would sync offline searches when back online
  } catch (error) {
    console.error('Service Worker: Error syncing property searches', error);
  }
}

// Sync offline report generation requests
async function syncReportGeneration() {
  try {
    console.log('Service Worker: Syncing offline report generation');
    // Implementation would sync offline report requests when back online
  } catch (error) {
    console.error('Service Worker: Error syncing report generation', error);
  }
}

// Push notification handling
self.addEventListener('push', (event) => {
  console.log('Service Worker: Push notification received');
  
  const options = {
    body: 'You have new property alerts',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: 'View Properties',
        icon: '/icons/checkmark.png'
      },
      {
        action: 'close',
        title: 'Close',
        icon: '/icons/xmark.png'
      }
    ]
  };

  if (event.data) {
    const data = event.data.json();
    options.body = data.body || options.body;
    options.data = { ...options.data, ...data };
  }

  event.waitUntil(
    self.registration.showNotification('Real Estate Automation', options)
  );
});

// Notification click handling
self.addEventListener('notificationclick', (event) => {
  console.log('Service Worker: Notification clicked', event.action);
  
  event.notification.close();

  if (event.action === 'explore') {
    event.waitUntil(
      clients.openWindow('/properties/search')
    );
  } else if (event.action === 'close') {
    // Just close the notification
  } else {
    // Default action - open the app
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});

// Message handling from main thread
self.addEventListener('message', (event) => {
  console.log('Service Worker: Message received', event.data);

  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  } else if (event.data && event.data.type === 'CACHE_PROPERTY_DATA') {
    // Cache property data for offline access
    cachePropertyData(event.data.payload);
  } else if (event.data && event.data.type === 'AUTH_STATE_CHANGE') {
    // Update authentication state
    isAuthenticated = event.data.isAuthenticated;
    console.log('Service Worker: Authentication state updated', isAuthenticated);

    // Clear auth-related caches when user signs out
    if (!isAuthenticated) {
      clearAuthRelatedCaches();
    }
  } else if (event.data && event.data.type === 'CLEAR_AUTH_CACHES') {
    // Clear authentication-related caches
    clearAuthRelatedCaches();
  }
});

// Cache property data for offline access
async function cachePropertyData(propertyData) {
  try {
    const cache = await caches.open(DYNAMIC_CACHE_NAME);
    const response = new Response(JSON.stringify(propertyData), {
      headers: { 'Content-Type': 'application/json' }
    });
    await cache.put(`/offline/property/${propertyData.id}`, response);
    console.log('Service Worker: Property data cached for offline access');
  } catch (error) {
    console.error('Service Worker: Error caching property data', error);
  }
}

// Clear authentication-related caches
async function clearAuthRelatedCaches() {
  try {
    const cache = await caches.open(DYNAMIC_CACHE_NAME);
    const keys = await cache.keys();

    // Remove cached API responses that require authentication
    const authRelatedPatterns = [
      /\/api\/reports\//,
      /\/api\/properties\/evaluate/,
      /\/api\/analytics\//,
      /\/api\/auth\//,
      /protected/
    ];

    const deletePromises = keys
      .filter(request => {
        const url = new URL(request.url);
        return authRelatedPatterns.some(pattern => pattern.test(url.pathname));
      })
      .map(request => cache.delete(request));

    await Promise.all(deletePromises);
    console.log('Service Worker: Authentication-related caches cleared');
  } catch (error) {
    console.error('Service Worker: Error clearing auth-related caches', error);
  }
}
