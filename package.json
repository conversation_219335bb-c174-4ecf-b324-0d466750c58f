{"name": "aquisitions-automation", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@auth/mongodb-adapter": "^3.7.4", "@hookform/resolvers": "^3.9.1", "@tanstack/react-query": "^5.62.7", "@types/puppeteer": "^5.4.7", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^11.15.0", "lucide-react": "^0.468.0", "mongodb": "^6.12.0", "mongoose": "^8.8.4", "next": "^14.2.15", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "openai": "^4.77.3", "puppeteer": "^23.10.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.4.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.1", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@types/bcryptjs": "^2.4.6", "@types/google.maps": "^3.58.1", "@types/node": "^22.10.2", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-config-next": "^15.1.3", "postcss": "^8.5.1", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9", "tailwindcss": "^3.4.17", "typescript": "^5.7.2"}}