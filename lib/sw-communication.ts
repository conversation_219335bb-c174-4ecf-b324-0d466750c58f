/**
 * Service Worker Communication Utility
 * Handles communication between the main thread and service worker
 */

export interface SWMessage {
  type: string;
  payload?: any;
  isAuthenticated?: boolean;
}

/**
 * Send a message to the service worker
 * @param message - The message to send
 * @returns Promise<boolean> - Success status
 */
export const sendMessageToSW = async (message: SWMessage): Promise<boolean> => {
  if (!('serviceWorker' in navigator)) {
    console.warn('Service Worker not supported in this browser');
    return false;
  }
  
  try {
    const registration = await navigator.serviceWorker.ready;
    if (registration.active) {
      registration.active.postMessage(message);
      console.log('Message sent to Service Worker:', message);
      return true;
    } else {
      console.warn('No active service worker found');
      return false;
    }
  } catch (error) {
    console.error('Failed to send message to Service Worker:', error);
    return false;
  }
};

/**
 * Update authentication state in service worker
 * @param isAuthenticated - Whether the user is authenticated
 * @returns Promise<boolean> - Success status
 */
export const updateSWAuthState = async (isAuthenticated: boolean): Promise<boolean> => {
  return sendMessageToSW({
    type: 'AUTH_STATE_CHANGE',
    isAuthenticated
  });
};

/**
 * Cache property data in service worker
 * @param propertyData - Property data to cache
 * @returns Promise<boolean> - Success status
 */
export const cachePropertyData = async (propertyData: any): Promise<boolean> => {
  return sendMessageToSW({
    type: 'CACHE_PROPERTY_DATA',
    payload: propertyData
  });
};

/**
 * Clear authentication-related caches
 * @returns Promise<boolean> - Success status
 */
export const clearAuthCaches = async (): Promise<boolean> => {
  return sendMessageToSW({
    type: 'CLEAR_AUTH_CACHES'
  });
};

/**
 * Check if service worker is available and ready
 * @returns Promise<boolean> - Availability status
 */
export const isSWAvailable = async (): Promise<boolean> => {
  if (!('serviceWorker' in navigator)) {
    return false;
  }
  
  try {
    const registration = await navigator.serviceWorker.ready;
    return !!registration.active;
  } catch (error) {
    console.error('Error checking service worker availability:', error);
    return false;
  }
};

/**
 * Get current authentication state from cookies
 * @returns boolean - Whether user appears to be authenticated
 */
export const getAuthStateFromCookies = (): boolean => {
  if (typeof document === 'undefined') {
    return false;
  }
  
  // Check for NextAuth session tokens
  return document.cookie.includes('next-auth.session-token') || 
         document.cookie.includes('__Secure-next-auth.session-token');
};
