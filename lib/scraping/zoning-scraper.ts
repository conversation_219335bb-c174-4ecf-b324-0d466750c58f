import { BaseScraper, ScrapedData } from './base-scraper';
import { Page } from 'puppeteer';

export interface ZoningData {
  zoneCode: string;
  zoneName: string;
  description: string;
  allowedUses: string[];
  restrictions: {
    maxHeight: number;
    maxDensity?: number;
    setbacks: {
      front: number;
      rear: number;
      side: number;
    };
    parkingRequirements: string;
    lotCoverage: number;
  };
  overlayDistricts: string[];
  specialConditions: string[];
  densityBonuses: Array<{
    type: string;
    description: string;
    bonus: string;
    requirements: string[];
  }>;
  developmentStandards: {
    minLotSize: number;
    minLotWidth: number;
    floorAreaRatio: number;
  };
}

export class ZoningScraper extends BaseScraper {
  private cityConfigs: { [key: string]: CityConfig } = {
    'los-angeles': {
      baseUrl: 'https://zimas.lacity.org',
      searchPath: '/search',
      selectors: {
        addressInput: '#address-search',
        searchButton: '.search-submit',
        zoningInfo: '.zoning-details',
        zoneCode: '.zone-code',
        zoneName: '.zone-name',
        allowedUses: '.allowed-uses li',
        restrictions: '.restrictions',
        overlays: '.overlay-districts li',
      }
    },
    'santa-monica': {
      baseUrl: 'https://www.smgov.net/zoning',
      searchPath: '/lookup',
      selectors: {
        addressInput: '',    // TODO: Replace with actual selector
        searchButton: '',    // TODO: Replace with actual selector
        zoningInfo: '',      // TODO: Replace with actual selector
        zoneCode: '',        // TODO: Replace with actual selector
        zoneName: '',        // TODO: Replace with actual selector
        allowedUses: '',     // TODO: Replace with actual selector
        restrictions: '',    // TODO: Replace with actual selector
        overlays: '',        // TODO: Replace with actual selector
      }
    },
    // Add more cities as needed
  };

  async scrape(address: string, city: string = 'los-angeles'): Promise<ScrapedData> {
    const config = this.cityConfigs[city];
    if (!config) {
      throw new Error(`City configuration not found: ${city}`);
    }

    let page: Page | null = null;
    
    try {
      page = await this.createPage();
      const url = `${config.baseUrl}${config.searchPath}`;
      
      await this.navigateWithRetry(page, url);
      
      // Handle potential bot protection
      if (await this.checkForBotProtection(page)) {
        await this.handleCaptcha(page);
      }

      const data = await this.scrapeZoningData(page, address, config);
      
      return {
        source: 'zoning-department',
        url,
        data,
        scrapedAt: new Date(),
        success: true,
      };
    } catch (error) {
      await this.handleError(error as Error, 'zoning-scraper');
      
      return {
        source: 'zoning-department',
        url: config.baseUrl,
        data: null,
        scrapedAt: new Date(),
        success: false,
        error: (error as Error).message,
      };
    } finally {
      if (page) {
        await page.close();
      }
    }
  }

  private async scrapeZoningData(
    page: Page, 
    address: string, 
    config: CityConfig
  ): Promise<ZoningData> {
    // Search for the address
    await this.waitForElementWithRetry(page, config.selectors.addressInput);
    await page.type(config.selectors.addressInput, address);
    await page.click(config.selectors.searchButton);
    
    // Wait for results
    await this.waitForElementWithRetry(page, config.selectors.zoningInfo);
    
    // Extract basic zoning information
    const zoneCode = await this.extractText(page, config.selectors.zoneCode) || '';
    const zoneName = await this.extractText(page, config.selectors.zoneName) || '';
    const description = await this.extractText(page, '.zone-description') || '';
    
    // Extract allowed uses
    const allowedUses = await this.extractTextArray(page, config.selectors.allowedUses);
    
    // Extract restrictions
    const restrictions = await this.extractRestrictions(page);
    
    // Extract overlay districts
    const overlayDistricts = await this.extractTextArray(page, config.selectors.overlays);
    
    // Extract special conditions
    const specialConditions = await this.extractTextArray(page, '.special-conditions li');
    
    // Extract density bonuses
    const densityBonuses = await this.extractDensityBonuses(page);
    
    // Extract development standards
    const developmentStandards = await this.extractDevelopmentStandards(page);
    
    return {
      zoneCode,
      zoneName,
      description,
      allowedUses,
      restrictions,
      overlayDistricts,
      specialConditions,
      densityBonuses,
      developmentStandards,
    };
  }

  private async extractTextArray(page: Page, selector: string): Promise<string[]> {
    try {
      const elements = await page.$$(selector);
      const texts: string[] = [];
      
      for (const element of elements) {
        const text = await page.evaluate(el => el.textContent?.trim(), element);
        if (text) {
          texts.push(text);
        }
      }
      
      return texts;
    } catch (error) {
      console.warn(`Failed to extract text array from ${selector}:`, error);
      return [];
    }
  }

  private async extractRestrictions(page: Page): Promise<ZoningData['restrictions']> {
    const defaultRestrictions = {
      maxHeight: 0,
      setbacks: { front: 0, rear: 0, side: 0 },
      parkingRequirements: '',
      lotCoverage: 0,
    };

    try {
      // Extract height restrictions
      const heightText = await this.extractText(page, '.max-height') || '0';
      const maxHeight = this.parseNumber(heightText);
      
      // Extract density if available
      const densityText = await this.extractText(page, '.max-density');
      const maxDensity = densityText ? this.parseNumber(densityText) : undefined;
      
      // Extract setbacks
      const frontSetbackText = await this.extractText(page, '.setback-front') || '0';
      const rearSetbackText = await this.extractText(page, '.setback-rear') || '0';
      const sideSetbackText = await this.extractText(page, '.setback-side') || '0';
      
      const setbacks = {
        front: this.parseNumber(frontSetbackText),
        rear: this.parseNumber(rearSetbackText),
        side: this.parseNumber(sideSetbackText),
      };
      
      // Extract parking requirements
      const parkingRequirements = await this.extractText(page, '.parking-requirements') || '';
      
      // Extract lot coverage
      const lotCoverageText = await this.extractText(page, '.lot-coverage') || '0';
      const lotCoverage = this.parseNumber(lotCoverageText);
      
      return {
        maxHeight,
        maxDensity,
        setbacks,
        parkingRequirements,
        lotCoverage,
      };
    } catch (error) {
      console.warn('Failed to extract restrictions:', error);
      return defaultRestrictions;
    }
  }

  private async extractDensityBonuses(page: Page): Promise<ZoningData['densityBonuses']> {
    const bonuses: ZoningData['densityBonuses'] = [];

    try {
      const bonusElements = await page.$$('.density-bonus');
      
      for (const element of bonusElements) {
        const type = await page.evaluate(el => 
          el.querySelector('.bonus-type')?.textContent?.trim(), element) || '';
        
        const description = await page.evaluate(el => 
          el.querySelector('.bonus-description')?.textContent?.trim(), element) || '';
        
        const bonus = await page.evaluate(el => 
          el.querySelector('.bonus-amount')?.textContent?.trim(), element) || '';
        
        const requirementElements = await element.$$('.requirement');
        const requirements: string[] = [];
        
        for (const reqElement of requirementElements) {
          const requirement = await page.evaluate(el => el.textContent?.trim(), reqElement);
          if (requirement) {
            requirements.push(requirement);
          }
        }
        
        if (type && description) {
          bonuses.push({
            type,
            description,
            bonus,
            requirements,
          });
        }
      }
    } catch (error) {
      console.warn('Failed to extract density bonuses:', error);
    }

    return bonuses;
  }

  private async extractDevelopmentStandards(page: Page): Promise<ZoningData['developmentStandards']> {
    const defaultStandards = {
      minLotSize: 0,
      minLotWidth: 0,
      floorAreaRatio: 0,
    };

    try {
      const minLotSizeText = await this.extractText(page, '.min-lot-size') || '0';
      const minLotSize = this.parseNumber(minLotSizeText);
      
      const minLotWidthText = await this.extractText(page, '.min-lot-width') || '0';
      const minLotWidth = this.parseNumber(minLotWidthText);
      
      const farText = await this.extractText(page, '.floor-area-ratio') || '0';
      const floorAreaRatio = parseFloat(farText.replace(/[^\d.]/g, '')) || 0;
      
      return {
        minLotSize,
        minLotWidth,
        floorAreaRatio,
      };
    } catch (error) {
      console.warn('Failed to extract development standards:', error);
      return defaultStandards;
    }
  }

  private parseNumber(text: string): number {
    const cleaned = text.replace(/[^\d.]/g, '');
    const number = parseFloat(cleaned);
    return isNaN(number) ? 0 : number;
  }

  /**
   * Detect city based on coordinates or address
   */
  static detectCity(coordinates?: { lat: number; lng: number }, address?: string): string {
    if (address) {
      const addressLower = address.toLowerCase();
      
      if (addressLower.includes('santa monica')) {
        return 'santa-monica';
      }
      if (addressLower.includes('beverly hills')) {
        return 'beverly-hills';
      }
      if (addressLower.includes('west hollywood')) {
        return 'west-hollywood';
      }
      // Add more city detection logic
    }

    if (coordinates) {
      // Santa Monica bounds
      if (coordinates.lat >= 34.0 && coordinates.lat <= 34.05 && 
          coordinates.lng >= -118.52 && coordinates.lng <= -118.46) {
        return 'santa-monica';
      }
      // Add more coordinate-based detection
    }

    // Default to Los Angeles
    return 'los-angeles';
  }
}

interface CityConfig {
  baseUrl: string;
  searchPath: string;
  selectors: {
    addressInput: string;
    searchButton: string;
    zoningInfo: string;
    zoneCode: string;
    zoneName: string;
    allowedUses: string;
    restrictions: string;
    overlays: string;
    [key: string]: string;
  };
}
