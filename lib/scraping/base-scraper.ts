import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';

export interface ScrapingConfig {
  headless?: boolean;
  timeout?: number;
  userAgent?: string;
  viewport?: {
    width: number;
    height: number;
  };
  proxy?: string;
  retries?: number;
  delay?: number;
}

export interface ScrapedData {
  source: string;
  url: string;
  data: any;
  scrapedAt: Date;
  success: boolean;
  error?: string;
}

export abstract class BaseScraper {
  protected browser: Browser | null = null;
  protected config: ScrapingConfig;

  constructor(config: ScrapingConfig = {}) {
    this.config = {
      headless: true,
      timeout: 30000,
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      viewport: { width: 1920, height: 1080 },
      retries: 3,
      delay: 1000,
      ...config,
    };
  }

  /**
   * Initialize the browser
   */
  async initialize(): Promise<void> {
    if (this.browser) return;

    const launchOptions: any = {
      headless: this.config.headless,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
      ],
    };

    if (this.config.proxy) {
      launchOptions.args.push(`--proxy-server=${this.config.proxy}`);
    }

    this.browser = await puppeteer.launch(launchOptions);
  }

  /**
   * Create a new page with configured settings
   */
  async createPage(): Promise<Page> {
    if (!this.browser) {
      await this.initialize();
    }

    const page = await this.browser!.newPage();
    
    await page.setUserAgent(this.config.userAgent!);
    await page.setViewport(this.config.viewport!);
    
    // Set timeout
    page.setDefaultTimeout(this.config.timeout!);
    
    // Block unnecessary resources to speed up scraping
    await page.setRequestInterception(true);
    page.on('request', (req) => {
      const resourceType = req.resourceType();
      if (['image', 'stylesheet', 'font', 'media'].includes(resourceType)) {
        req.abort();
      } else {
        req.continue();
      }
    });

    return page;
  }

  /**
   * Navigate to URL with retry logic
   */
  async navigateWithRetry(page: Page, url: string): Promise<void> {
    let lastError: Error | null = null;
    
    for (let i = 0; i < this.config.retries!; i++) {
      try {
        await page.goto(url, { 
          waitUntil: 'networkidle2',
          timeout: this.config.timeout 
        });
        return;
      } catch (error) {
        lastError = error as Error;
        console.warn(`Navigation attempt ${i + 1} failed:`, error);
        
        if (i < this.config.retries! - 1) {
          await this.delay(this.config.delay! * (i + 1));
        }
      }
    }
    
    throw lastError;
  }

  /**
   * Wait for element with retry logic
   */
  async waitForElementWithRetry(page: Page, selector: string): Promise<void> {
    let lastError: Error | null = null;
    
    for (let i = 0; i < this.config.retries!; i++) {
      try {
        await page.waitForSelector(selector, { timeout: this.config.timeout });
        return;
      } catch (error) {
        lastError = error as Error;
        
        if (i < this.config.retries! - 1) {
          await this.delay(this.config.delay!);
        }
      }
    }
    
    throw lastError;
  }

  /**
   * Extract text content safely
   */
  async extractText(page: Page, selector: string): Promise<string | null> {
    try {
      const element = await page.$(selector);
      if (!element) return null;
      
      const text = await page.evaluate(el => el.textContent?.trim(), element);
      return text || null;
    } catch (error) {
      console.warn(`Failed to extract text from ${selector}:`, error);
      return null;
    }
  }

  /**
   * Extract attribute safely
   */
  async extractAttribute(page: Page, selector: string, attribute: string): Promise<string | null> {
    try {
      const element = await page.$(selector);
      if (!element) return null;
      
      const value = await page.evaluate(
        (el, attr) => el.getAttribute(attr),
        element,
        attribute
      );
      return value;
    } catch (error) {
      console.warn(`Failed to extract attribute ${attribute} from ${selector}:`, error);
      return null;
    }
  }

  /**
   * Handle CAPTCHA (placeholder for integration with solving services)
   */
  async handleCaptcha(page: Page): Promise<boolean> {
    // Check for common CAPTCHA indicators
    const captchaSelectors = [
      '.g-recaptcha',
      '#captcha',
      '.captcha',
      '[data-captcha]',
    ];

    for (const selector of captchaSelectors) {
      const captchaElement = await page.$(selector);
      if (captchaElement) {
        console.log('CAPTCHA detected, attempting to solve...');
        
        // In a real implementation, integrate with services like:
        // - 2captcha
        // - Anti-Captcha
        // - DeathByCaptcha
        
        // For now, just wait and hope it goes away
        await this.delay(5000);
        return true;
      }
    }

    return false;
  }

  /**
   * Rotate user agent
   */
  async rotateUserAgent(page: Page): Promise<void> {
    const userAgents = [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
    ];

    const randomUserAgent = userAgents[Math.floor(Math.random() * userAgents.length)];
    await page.setUserAgent(randomUserAgent);
  }

  /**
   * Add random delay to mimic human behavior
   */
  async delay(ms: number): Promise<void> {
    const randomDelay = ms + Math.random() * 1000; // Add up to 1 second of randomness
    return new Promise(resolve => setTimeout(resolve, randomDelay));
  }

  /**
   * Clean up resources
   */
  async cleanup(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }

  /**
   * Abstract method to be implemented by specific scrapers
   */
  abstract scrape(url: string, params?: any): Promise<ScrapedData>;

  /**
   * Rate limiting helper
   */
  protected async respectRateLimit(): Promise<void> {
    await this.delay(this.config.delay!);
  }

  /**
   * Check if page has anti-bot protection
   */
  async checkForBotProtection(page: Page): Promise<boolean> {
    const protectionIndicators = [
      'cloudflare',
      'captcha',
      'bot protection',
      'access denied',
      'blocked',
    ];

    const pageContent = await page.content().toLowerCase();
    
    return protectionIndicators.some(indicator => 
      pageContent.includes(indicator)
    );
  }

  /**
   * Handle common errors and retries
   */
  protected async handleError(error: Error, context: string): Promise<void> {
    console.error(`Error in ${context}:`, error.message);
    
    // Log error for monitoring
    // In production, send to error tracking service
    
    if (error.message.includes('timeout')) {
      console.log('Timeout detected, implementing backoff strategy');
      await this.delay(5000);
    }
  }
}

// Rate limiter for managing request frequency
export class RateLimiter {
  private requests: number[] = [];
  private maxRequests: number;
  private timeWindow: number;

  constructor(maxRequests: number = 10, timeWindowMs: number = 60000) {
    this.maxRequests = maxRequests;
    this.timeWindow = timeWindowMs;
  }

  async waitIfNeeded(): Promise<void> {
    const now = Date.now();
    
    // Remove old requests outside the time window
    this.requests = this.requests.filter(time => now - time < this.timeWindow);
    
    if (this.requests.length >= this.maxRequests) {
      const oldestRequest = Math.min(...this.requests);
      const waitTime = this.timeWindow - (now - oldestRequest);
      
      if (waitTime > 0) {
        console.log(`Rate limit reached, waiting ${waitTime}ms`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
    
    this.requests.push(now);
  }
}
