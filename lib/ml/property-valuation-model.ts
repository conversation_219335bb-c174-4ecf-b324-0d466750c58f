import { Property, EvaluationResults } from '@/types';

export interface PropertyFeatures {
  // Location features
  latitude: number;
  longitude: number;
  distanceToTransit: number;
  distanceToDowntown: number;
  neighborhoodScore: number;
  
  // Property features
  lotSize: number;
  lotWidth: number;
  lotLength: number;
  currentZoning: string;
  zoningScore: number;
  
  // Development potential
  maxDensity: number;
  maxHeight: number;
  setbackRequirements: number;
  developmentPotentialScore: number;
  
  // Market features
  medianHomePrice: number;
  pricePerSqFt: number;
  marketTrend: number;
  daysOnMarket: number;
  
  // Risk factors
  environmentalRisk: number;
  historicalDesignation: number;
  floodZone: number;
  seismicRisk: number;
  
  // Financial features
  assessedValue: number;
  taxRate: number;
  utilityAvailability: number;
  constructionCosts: number;
}

export interface ValuationPrediction {
  estimatedValue: number;
  confidence: number;
  valueRange: {
    low: number;
    high: number;
  };
  factors: {
    positive: Array<{ factor: string; impact: number; description: string }>;
    negative: Array<{ factor: string; impact: number; description: string }>;
  };
  comparables: Array<{
    address: string;
    value: number;
    similarity: number;
    adjustments: Record<string, number>;
  }>;
}

export interface RiskAssessment {
  overallRisk: 'low' | 'medium' | 'high';
  riskScore: number; // 0-100
  riskFactors: Array<{
    category: string;
    risk: 'low' | 'medium' | 'high';
    score: number;
    description: string;
    mitigation?: string;
  }>;
  recommendations: string[];
}

export interface MarketPrediction {
  priceDirection: 'up' | 'down' | 'stable';
  expectedChange: number; // percentage
  timeHorizon: number; // months
  confidence: number;
  marketFactors: Array<{
    factor: string;
    impact: 'positive' | 'negative' | 'neutral';
    weight: number;
  }>;
}

export class PropertyValuationModel {
  private modelWeights: Record<string, number>;
  private marketData: Map<string, any> = new Map();
  private comparableProperties: Property[] = [];

  constructor() {
    this.modelWeights = this.initializeModelWeights();
    this.loadMarketData();
  }

  /**
   * Predict property value using ML model
   */
  async predictValue(property: Property, evaluation?: EvaluationResults): Promise<ValuationPrediction> {
    const features = this.extractFeatures(property, evaluation);
    const baseValue = this.calculateBaseValue(features);
    const adjustments = this.calculateAdjustments(features);
    const estimatedValue = baseValue + adjustments.total;
    
    const confidence = this.calculateConfidence(features, adjustments);
    const valueRange = this.calculateValueRange(estimatedValue, confidence);
    
    const factors = this.identifyValueFactors(features, adjustments);
    const comparables = await this.findComparables(property, features);

    return {
      estimatedValue,
      confidence,
      valueRange,
      factors,
      comparables
    };
  }

  /**
   * Assess investment risk
   */
  assessRisk(property: Property, evaluation?: EvaluationResults): RiskAssessment {
    const features = this.extractFeatures(property, evaluation);
    const riskFactors = this.calculateRiskFactors(features);
    const overallScore = this.calculateOverallRisk(riskFactors);
    
    return {
      overallRisk: this.categorizeRisk(overallScore),
      riskScore: overallScore,
      riskFactors,
      recommendations: this.generateRiskRecommendations(riskFactors)
    };
  }

  /**
   * Predict market trends
   */
  predictMarketTrends(property: Property): MarketPrediction {
    const locationKey = this.getLocationKey(property.coordinates);
    const marketData = this.marketData.get(locationKey) || this.getDefaultMarketData();
    
    const marketFactors = this.analyzeMarketFactors(marketData);
    const expectedChange = this.calculateExpectedChange(marketFactors);
    const confidence = this.calculateMarketConfidence(marketFactors);
    
    return {
      priceDirection: expectedChange > 2 ? 'up' : expectedChange < -2 ? 'down' : 'stable',
      expectedChange,
      timeHorizon: 12, // 12 months
      confidence,
      marketFactors
    };
  }

  /**
   * Extract features from property data
   */
  private extractFeatures(property: Property, evaluation?: EvaluationResults): PropertyFeatures {
    return {
      // Location features
      latitude: property.coordinates.lat,
      longitude: property.coordinates.lng,
      distanceToTransit: evaluation?.criteria.transitAccess?.nearestStopDistance || 1.0,
      distanceToDowntown: this.calculateDistanceToDowntown(property.coordinates),
      neighborhoodScore: this.calculateNeighborhoodScore(property.coordinates),
      
      // Property features
      lotSize: property.lotSize,
      lotWidth: property.dimensions?.width || Math.sqrt(property.lotSize),
      lotLength: property.dimensions?.length || Math.sqrt(property.lotSize),
      currentZoning: property.zoning,
      zoningScore: this.calculateZoningScore(property.zoning),
      
      // Development potential
      maxDensity: evaluation?.criteria.densityPotential?.totalUnits || 0,
      maxHeight: evaluation?.criteria.heightRestrictions?.totalHeight || 0,
      setbackRequirements: 10, // Default setback
      developmentPotentialScore: evaluation?.overallScore || 0,
      
      // Market features
      medianHomePrice: this.getMedianHomePrice(property.coordinates),
      pricePerSqFt: property.assessedValue / property.lotSize,
      marketTrend: this.getMarketTrend(property.coordinates),
      daysOnMarket: 30, // Default
      
      // Risk factors
      environmentalRisk: evaluation?.criteria.environmentalConcerns?.hasIssues ? 100 : 0,
      historicalDesignation: evaluation?.criteria.historicalStatus?.hasDesignation ? 100 : 0,
      floodZone: 0, // Would be determined from FEMA data
      seismicRisk: this.calculateSeismicRisk(property.coordinates),
      
      // Financial features
      assessedValue: property.assessedValue,
      taxRate: (property.taxAmount / property.assessedValue) * 100,
      utilityAvailability: 100, // Assume full utility availability
      constructionCosts: this.estimateConstructionCosts(property.coordinates)
    };
  }

  /**
   * Calculate base property value
   */
  private calculateBaseValue(features: PropertyFeatures): number {
    let baseValue = 0;
    
    // Land value based on size and location
    const landValuePerSqFt = features.medianHomePrice / 7500; // Assume 7500 sq ft average lot
    baseValue += features.lotSize * landValuePerSqFt;
    
    // Development potential premium
    if (features.maxDensity > 100) {
      baseValue *= 1.2; // 20% premium for high density potential
    }
    
    // Location adjustments
    baseValue *= (1 + features.neighborhoodScore / 100);
    
    return baseValue;
  }

  /**
   * Calculate value adjustments
   */
  private calculateAdjustments(features: PropertyFeatures): { total: number; breakdown: Record<string, number> } {
    const adjustments: Record<string, number> = {};
    
    // Transit accessibility adjustment
    if (features.distanceToTransit < 0.25) {
      adjustments.transit = features.assessedValue * 0.15; // 15% premium
    } else if (features.distanceToTransit > 1.0) {
      adjustments.transit = features.assessedValue * -0.1; // 10% discount
    }
    
    // Zoning adjustment
    adjustments.zoning = features.zoningScore * 1000;
    
    // Risk adjustments
    adjustments.environmental = -features.environmentalRisk * 500;
    adjustments.historical = -features.historicalDesignation * 300;
    adjustments.seismic = -features.seismicRisk * 200;
    
    // Market trend adjustment
    adjustments.marketTrend = features.assessedValue * (features.marketTrend / 100);
    
    const total = Object.values(adjustments).reduce((sum, adj) => sum + adj, 0);
    
    return { total, breakdown: adjustments };
  }

  /**
   * Calculate confidence score
   */
  private calculateConfidence(features: PropertyFeatures, adjustments: any): number {
    let confidence = 80; // Base confidence
    
    // Reduce confidence for high-risk properties
    if (features.environmentalRisk > 50) confidence -= 20;
    if (features.historicalDesignation > 0) confidence -= 15;
    
    // Increase confidence for well-located properties
    if (features.distanceToTransit < 0.25) confidence += 10;
    if (features.neighborhoodScore > 80) confidence += 10;
    
    return Math.max(50, Math.min(95, confidence));
  }

  /**
   * Calculate value range based on confidence
   */
  private calculateValueRange(estimatedValue: number, confidence: number): { low: number; high: number } {
    const variance = (100 - confidence) / 100 * 0.3; // Max 30% variance
    
    return {
      low: estimatedValue * (1 - variance),
      high: estimatedValue * (1 + variance)
    };
  }

  /**
   * Identify positive and negative value factors
   */
  private identifyValueFactors(features: PropertyFeatures, adjustments: any): ValuationPrediction['factors'] {
    const positive: Array<{ factor: string; impact: number; description: string }> = [];
    const negative: Array<{ factor: string; impact: number; description: string }> = [];
    
    // Analyze each adjustment
    Object.entries(adjustments.breakdown).forEach(([factor, impact]) => {
      const impactValue = Math.abs(impact as number);
      const description = this.getFactorDescription(factor, impact as number);
      
      if ((impact as number) > 1000) {
        positive.push({ factor, impact: impactValue, description });
      } else if ((impact as number) < -1000) {
        negative.push({ factor, impact: impactValue, description });
      }
    });
    
    return { positive, negative };
  }

  /**
   * Find comparable properties
   */
  private async findComparables(property: Property, features: PropertyFeatures): Promise<ValuationPrediction['comparables']> {
    // Mock comparable properties for demo
    return [
      {
        address: '456 Similar St, Los Angeles, CA',
        value: features.assessedValue * 1.1,
        similarity: 0.85,
        adjustments: { size: 0.05, location: 0.03, zoning: 0.02 }
      },
      {
        address: '789 Nearby Ave, Los Angeles, CA',
        value: features.assessedValue * 0.95,
        similarity: 0.78,
        adjustments: { size: -0.03, location: -0.02, condition: -0.05 }
      }
    ];
  }

  /**
   * Calculate risk factors
   */
  private calculateRiskFactors(features: PropertyFeatures): RiskAssessment['riskFactors'] {
    const factors: RiskAssessment['riskFactors'] = [];
    
    // Environmental risk
    if (features.environmentalRisk > 0) {
      factors.push({
        category: 'Environmental',
        risk: features.environmentalRisk > 70 ? 'high' : features.environmentalRisk > 30 ? 'medium' : 'low',
        score: features.environmentalRisk,
        description: 'Potential environmental contamination or hazards',
        mitigation: 'Conduct Phase I/II environmental assessment'
      });
    }
    
    // Market risk
    const marketRisk = Math.abs(features.marketTrend) > 10 ? 'high' : Math.abs(features.marketTrend) > 5 ? 'medium' : 'low';
    factors.push({
      category: 'Market',
      risk: marketRisk,
      score: Math.abs(features.marketTrend) * 5,
      description: `Market showing ${features.marketTrend > 0 ? 'growth' : 'decline'} trend`,
      mitigation: 'Monitor market conditions and adjust timeline accordingly'
    });
    
    // Regulatory risk
    if (features.historicalDesignation > 0) {
      factors.push({
        category: 'Regulatory',
        risk: 'high',
        score: 80,
        description: 'Property has historical designation restrictions',
        mitigation: 'Consult with preservation specialists and city planning'
      });
    }
    
    return factors;
  }

  /**
   * Calculate overall risk score
   */
  private calculateOverallRisk(riskFactors: RiskAssessment['riskFactors']): number {
    if (riskFactors.length === 0) return 20; // Low baseline risk
    
    const weightedRisk = riskFactors.reduce((total, factor) => {
      const weight = factor.category === 'Environmental' ? 0.4 : 
                   factor.category === 'Market' ? 0.3 : 0.3;
      return total + (factor.score * weight);
    }, 0);
    
    return Math.min(100, weightedRisk);
  }

  /**
   * Categorize risk level
   */
  private categorizeRisk(score: number): 'low' | 'medium' | 'high' {
    if (score < 30) return 'low';
    if (score < 70) return 'medium';
    return 'high';
  }

  /**
   * Generate risk recommendations
   */
  private generateRiskRecommendations(riskFactors: RiskAssessment['riskFactors']): string[] {
    const recommendations: string[] = [];
    
    riskFactors.forEach(factor => {
      if (factor.mitigation) {
        recommendations.push(factor.mitigation);
      }
    });
    
    if (recommendations.length === 0) {
      recommendations.push('Proceed with standard due diligence procedures');
    }
    
    return recommendations;
  }

  /**
   * Helper methods for calculations
   */
  private calculateDistanceToDowntown(coordinates: { lat: number; lng: number }): number {
    // Distance to LA downtown (34.0522, -118.2437)
    const lat1 = coordinates.lat;
    const lon1 = coordinates.lng;
    const lat2 = 34.0522;
    const lon2 = -118.2437;
    
    const R = 3959; // Earth's radius in miles
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    
    return R * c;
  }

  private calculateNeighborhoodScore(coordinates: { lat: number; lng: number }): number {
    // Mock neighborhood scoring based on location
    const distanceToDowntown = this.calculateDistanceToDowntown(coordinates);
    return Math.max(0, 100 - (distanceToDowntown * 5));
  }

  private calculateZoningScore(zoning: string): number {
    const zoningScores: Record<string, number> = {
      'R1': 20,
      'R2': 40,
      'R3': 60,
      'R4': 80,
      'R5': 100,
      'C1': 70,
      'C2': 85,
      'M1': 50
    };
    return zoningScores[zoning] || 30;
  }

  private getMedianHomePrice(coordinates: { lat: number; lng: number }): number {
    // Mock median home price based on location
    const distanceToDowntown = this.calculateDistanceToDowntown(coordinates);
    return Math.max(400000, 1200000 - (distanceToDowntown * 50000));
  }

  private getMarketTrend(coordinates: { lat: number; lng: number }): number {
    // Mock market trend (percentage change)
    return Math.random() * 10 - 5; // -5% to +5%
  }

  private calculateSeismicRisk(coordinates: { lat: number; lng: number }): number {
    // Mock seismic risk for LA area
    return 30; // Moderate seismic risk
  }

  private estimateConstructionCosts(coordinates: { lat: number; lng: number }): number {
    // Mock construction costs per sq ft
    return 200; // $200 per sq ft
  }

  private getLocationKey(coordinates: { lat: number; lng: number }): string {
    return `${Math.round(coordinates.lat * 100)}_${Math.round(coordinates.lng * 100)}`;
  }

  private getDefaultMarketData(): any {
    return {
      priceGrowth: 5.2,
      inventory: 2.1,
      daysOnMarket: 28,
      priceToIncome: 8.5
    };
  }

  private analyzeMarketFactors(marketData: any): MarketPrediction['marketFactors'] {
    return [
      {
        factor: 'Price Growth',
        impact: marketData.priceGrowth > 5 ? 'positive' : 'negative',
        weight: 0.3
      },
      {
        factor: 'Inventory Levels',
        impact: marketData.inventory < 3 ? 'positive' : 'negative',
        weight: 0.25
      },
      {
        factor: 'Days on Market',
        impact: marketData.daysOnMarket < 30 ? 'positive' : 'negative',
        weight: 0.2
      }
    ];
  }

  private calculateExpectedChange(marketFactors: MarketPrediction['marketFactors']): number {
    return marketFactors.reduce((total, factor) => {
      const impact = factor.impact === 'positive' ? 1 : factor.impact === 'negative' ? -1 : 0;
      return total + (impact * factor.weight * 10);
    }, 0);
  }

  private calculateMarketConfidence(marketFactors: MarketPrediction['marketFactors']): number {
    return 75; // Mock confidence
  }

  private getFactorDescription(factor: string, impact: number): string {
    const descriptions: Record<string, string> = {
      transit: impact > 0 ? 'Excellent transit accessibility' : 'Limited transit access',
      zoning: impact > 0 ? 'Favorable zoning for development' : 'Restrictive zoning',
      environmental: 'Environmental concerns identified',
      historical: 'Historical designation restrictions',
      marketTrend: impact > 0 ? 'Positive market trends' : 'Declining market conditions'
    };
    return descriptions[factor] || 'Market factor impact';
  }

  private initializeModelWeights(): Record<string, number> {
    return {
      location: 0.3,
      development: 0.25,
      market: 0.2,
      risk: 0.15,
      financial: 0.1
    };
  }

  private loadMarketData(): void {
    // Mock market data loading
    this.marketData.set('3405_-11824', {
      priceGrowth: 6.2,
      inventory: 1.8,
      daysOnMarket: 25,
      priceToIncome: 9.1
    });
  }
}
