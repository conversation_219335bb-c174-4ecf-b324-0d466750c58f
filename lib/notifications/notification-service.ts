export interface Notification {
  id: string;
  userId: string;
  type: 'property_alert' | 'report_ready' | 'system_update' | 'market_update' | 'data_sync';
  title: string;
  message: string;
  data?: any;
  read: boolean;
  createdAt: Date;
  expiresAt?: Date;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  channels: ('email' | 'sms' | 'push' | 'in_app')[];
}

export interface NotificationPreferences {
  userId: string;
  emailNotifications: boolean;
  smsNotifications: boolean;
  pushNotifications: boolean;
  propertyAlerts: boolean;
  reportGeneration: boolean;
  marketUpdates: boolean;
  systemUpdates: boolean;
  weeklyDigest: boolean;
  frequency: 'immediate' | 'hourly' | 'daily' | 'weekly';
}

export interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  htmlContent: string;
  textContent: string;
  variables: string[];
}

export class NotificationService {
  private notifications: Map<string, Notification> = new Map();
  private preferences: Map<string, NotificationPreferences> = new Map();
  private templates: Map<string, EmailTemplate> = new Map();

  constructor() {
    this.initializeTemplates();
  }

  private initializeTemplates() {
    // Property Alert Template
    this.templates.set('property_alert', {
      id: 'property_alert',
      name: 'Property Alert',
      subject: 'New Property Match Found: {{propertyAddress}}',
      htmlContent: [
        '<h2>New Property Match Found!</h2>',
        '<p>We found a property that matches your criteria:</p>',
        '<div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0;">',
        '  <h3>{{propertyAddress}}</h3>',
        '  <p><strong>Lot Size:</strong> {{lotSize}} sq ft</p>',
        '  <p><strong>Zoning:</strong> {{zoning}}</p>',
        '  <p><strong>Assessed Value:</strong> ${{assessedValue}}</p>',
        '  <p><strong>Evaluation Score:</strong> {{evaluationScore}}/100</p>',
        '</div>',
        '<a href="{{propertyUrl}}" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">View Property Details</a>'
      ].join('\n'),
      textContent: [
        'New Property Match Found!',
        '',
        'Property: {{propertyAddress}}',
        'Lot Size: {{lotSize}} sq ft',
        'Zoning: {{zoning}}',
        'Assessed Value: ${{assessedValue}}',
        'Evaluation Score: {{evaluationScore}}/100',
        '',
        'View details: {{propertyUrl}}'
      ].join('\n'),
      variables: ['propertyAddress', 'lotSize', 'zoning', 'assessedValue', 'evaluationScore', 'propertyUrl'],
    });

    // Report Ready Template
    this.templates.set('report_ready', {
      id: 'report_ready',
      name: 'Report Ready',
      subject: 'Your Property Report is Ready: {{reportTitle}}',
      htmlContent: `
        <h2>Your Property Report is Ready!</h2>
        <p>The report you requested has been generated and is ready for download.</p>
        <div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0;">
          <h3>{{reportTitle}}</h3>
          <p><strong>Property:</strong> {{propertyAddress}}</p>
          <p><strong>Report Type:</strong> {{reportType}}</p>
          <p><strong>Generated:</strong> {{generatedAt}}</p>
          <p><strong>Confidence Level:</strong> {{confidenceLevel}}%</p>
        </div>
        <a href="{{reportUrl}}" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Download Report</a>
      `,
      textContent: `
        Your Property Report is Ready!
        
        Report: {{reportTitle}}
        Property: {{propertyAddress}}
        Type: {{reportType}}
        Generated: {{generatedAt}}
        Confidence: {{confidenceLevel}}%
        
        Download: {{reportUrl}}
      `,
      variables: ['reportTitle', 'propertyAddress', 'reportType', 'generatedAt', 'confidenceLevel', 'reportUrl'],
    });

    // Market Update Template
    this.templates.set('market_update', {
      id: 'market_update',
      name: 'Market Update',
      subject: 'Weekly Market Update - {{date}}',
      htmlContent: `
        <h2>Weekly Market Update</h2>
        <p>Here's what's happening in the real estate market this week:</p>
        <div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0;">
          <h3>Key Metrics</h3>
          <ul>
            <li><strong>New Properties:</strong> {{newProperties}} properties added</li>
            <li><strong>Qualified Properties:</strong> {{qualifiedProperties}} meet your criteria</li>
            <li><strong>Average Score:</strong> {{averageScore}}/100</li>
            <li><strong>Market Trend:</strong> {{marketTrend}}</li>
          </ul>
        </div>
        <a href="{{dashboardUrl}}" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">View Dashboard</a>
      `,
      textContent: `
        Weekly Market Update - {{date}}
        
        Key Metrics:
        - New Properties: {{newProperties}}
        - Qualified Properties: {{qualifiedProperties}}
        - Average Score: {{averageScore}}/100
        - Market Trend: {{marketTrend}}
        
        View Dashboard: {{dashboardUrl}}
      `,
      variables: ['date', 'newProperties', 'qualifiedProperties', 'averageScore', 'marketTrend', 'dashboardUrl'],
    });
  }

  async createNotification(notification: Omit<Notification, 'id' | 'createdAt'>): Promise<string> {
    const id = `notif_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    const fullNotification: Notification = {
      ...notification,
      id,
      createdAt: new Date(),
    };

    this.notifications.set(id, fullNotification);

    // Send notification through specified channels
    await this.sendNotification(fullNotification);

    return id;
  }

  private async sendNotification(notification: Notification) {
    const preferences = this.getUserPreferences(notification.userId);
    
    for (const channel of notification.channels) {
      if (this.shouldSendToChannel(channel, preferences, notification.type)) {
        switch (channel) {
          case 'email':
            await this.sendEmail(notification);
            break;
          case 'sms':
            await this.sendSMS(notification);
            break;
          case 'push':
            await this.sendPushNotification(notification);
            break;
          case 'in_app':
            // In-app notifications are stored and displayed in UI
            break;
        }
      }
    }
  }

  private shouldSendToChannel(
    channel: string,
    preferences: NotificationPreferences,
    type: string
  ): boolean {
    // Check if user has enabled this channel
    switch (channel) {
      case 'email':
        if (!preferences.emailNotifications) return false;
        break;
      case 'sms':
        if (!preferences.smsNotifications) return false;
        break;
      case 'push':
        if (!preferences.pushNotifications) return false;
        break;
    }

    // Check if user has enabled this notification type
    switch (type) {
      case 'property_alert':
        return preferences.propertyAlerts;
      case 'report_ready':
        return preferences.reportGeneration;
      case 'market_update':
        return preferences.marketUpdates;
      case 'system_update':
        return preferences.systemUpdates;
      default:
        return true;
    }
  }

  private async sendEmail(notification: Notification) {
    console.log(`Sending email notification: ${notification.title}`);
    
    // In production, integrate with email service (SendGrid, AWS SES, etc.)
    const template = this.templates.get(notification.type);
    if (template && notification.data) {
      const subject = this.replaceVariables(template.subject, notification.data);
      const htmlContent = this.replaceVariables(template.htmlContent, notification.data);
      const textContent = this.replaceVariables(template.textContent, notification.data);
      
      // Mock email sending
      console.log('Email sent:', { subject, htmlContent: htmlContent.substring(0, 100) + '...' });
    }
  }

  private async sendSMS(notification: Notification) {
    console.log(`Sending SMS notification: ${notification.title}`);
    
    // In production, integrate with SMS service (Twilio, AWS SNS, etc.)
    const message = `${notification.title}: ${notification.message}`;
    console.log('SMS sent:', message.substring(0, 160));
  }

  private async sendPushNotification(notification: Notification) {
    console.log(`Sending push notification: ${notification.title}`);
    
    // In production, integrate with push service (Firebase, OneSignal, etc.)
    console.log('Push notification sent:', {
      title: notification.title,
      body: notification.message,
    });
  }

  private replaceVariables(template: string, data: any): string {
    let result = template;
    for (const [key, value] of Object.entries(data)) {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, String(value));
    }
    return result;
  }

  getUserNotifications(userId: string, limit: number = 50): Notification[] {
    return Array.from(this.notifications.values())
      .filter(n => n.userId === userId)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      .slice(0, limit);
  }

  getUnreadCount(userId: string): number {
    return Array.from(this.notifications.values())
      .filter(n => n.userId === userId && !n.read)
      .length;
  }

  async markAsRead(notificationId: string): Promise<boolean> {
    const notification = this.notifications.get(notificationId);
    if (notification) {
      notification.read = true;
      return true;
    }
    return false;
  }

  async markAllAsRead(userId: string): Promise<number> {
    let count = 0;
    for (const notification of this.notifications.values()) {
      if (notification.userId === userId && !notification.read) {
        notification.read = true;
        count++;
      }
    }
    return count;
  }

  getUserPreferences(userId: string): NotificationPreferences {
    return this.preferences.get(userId) || {
      userId,
      emailNotifications: true,
      smsNotifications: false,
      pushNotifications: true,
      propertyAlerts: true,
      reportGeneration: true,
      marketUpdates: false,
      systemUpdates: true,
      weeklyDigest: true,
      frequency: 'immediate',
    };
  }

  updateUserPreferences(preferences: NotificationPreferences): void {
    this.preferences.set(preferences.userId, preferences);
  }

  async sendPropertyAlert(userId: string, propertyData: any): Promise<string> {
    return this.createNotification({
      userId,
      type: 'property_alert',
      title: `New Property Match: ${propertyData.address}`,
      message: `Found a property that matches your criteria with a score of ${propertyData.evaluationScore}/100`,
      data: {
        propertyAddress: propertyData.address,
        lotSize: propertyData.lotSize.toLocaleString(),
        zoning: propertyData.zoning,
        assessedValue: propertyData.assessedValue.toLocaleString(),
        evaluationScore: propertyData.evaluationScore,
        propertyUrl: `${process.env.NEXTAUTH_URL}/properties/${propertyData.id}`,
      },
      read: false,
      priority: 'medium',
      channels: ['email', 'in_app', 'push'],
    });
  }

  async sendReportReady(userId: string, reportData: any): Promise<string> {
    return this.createNotification({
      userId,
      type: 'report_ready',
      title: `Report Ready: ${reportData.title}`,
      message: `Your property report has been generated and is ready for download`,
      data: {
        reportTitle: reportData.title,
        propertyAddress: reportData.propertyAddress,
        reportType: reportData.type,
        generatedAt: new Date().toLocaleDateString(),
        confidenceLevel: reportData.confidenceLevel,
        reportUrl: `${process.env.NEXTAUTH_URL}/reports/${reportData.id}`,
      },
      read: false,
      priority: 'medium',
      channels: ['email', 'in_app'],
    });
  }

  async sendWeeklyDigest(userId: string, digestData: any): Promise<string> {
    return this.createNotification({
      userId,
      type: 'market_update',
      title: 'Weekly Market Update',
      message: `${digestData.newProperties} new properties found this week`,
      data: {
        date: new Date().toLocaleDateString(),
        newProperties: digestData.newProperties,
        qualifiedProperties: digestData.qualifiedProperties,
        averageScore: digestData.averageScore,
        marketTrend: digestData.marketTrend,
        dashboardUrl: `${process.env.NEXTAUTH_URL}/dashboard`,
      },
      read: false,
      priority: 'low',
      channels: ['email'],
    });
  }

  // Cleanup expired notifications
  cleanupExpiredNotifications(): number {
    const now = new Date();
    let cleaned = 0;
    
    for (const [id, notification] of this.notifications.entries()) {
      if (notification.expiresAt && notification.expiresAt < now) {
        this.notifications.delete(id);
        cleaned++;
      }
    }
    
    return cleaned;
  }
}
