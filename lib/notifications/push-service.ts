export interface PushSubscription {
  id: string;
  userId: string;
  endpoint: string;
  keys: {
    p256dh: string;
    auth: string;
  };
  userAgent: string;
  createdAt: Date;
  lastUsed: Date;
  active: boolean;
}

export interface PushNotification {
  id: string;
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  image?: string;
  data?: Record<string, any>;
  actions?: NotificationAction[];
  tag?: string;
  requireInteraction?: boolean;
  silent?: boolean;
  timestamp?: number;
}

export interface NotificationAction {
  action: string;
  title: string;
  icon?: string;
}

export interface PushMessage {
  id: string;
  subscriptionId: string;
  notification: PushNotification;
  status: 'pending' | 'sent' | 'delivered' | 'failed' | 'clicked';
  sentAt?: Date;
  deliveredAt?: Date;
  clickedAt?: Date;
  errorMessage?: string;
}

export interface PushTemplate {
  id: string;
  name: string;
  title: string;
  body: string;
  icon?: string;
  actions?: NotificationAction[];
  variables: string[];
  category: 'property_alert' | 'report_ready' | 'system_update' | 'reminder';
}

export class PushNotificationService {
  private subscriptions: Map<string, PushSubscription> = new Map();
  private templates: Map<string, PushTemplate> = new Map();
  private messageHistory: Map<string, PushMessage> = new Map();
  private vapidKeys: { publicKey: string; privateKey: string };

  constructor(vapidKeys?: { publicKey: string; privateKey: string }) {
    this.vapidKeys = vapidKeys || {
      publicKey: 'demo-public-key',
      privateKey: 'demo-private-key'
    };
    this.initializeTemplates();
  }

  /**
   * Subscribe user to push notifications
   */
  async subscribe(
    userId: string,
    subscription: {
      endpoint: string;
      keys: { p256dh: string; auth: string };
    },
    userAgent: string
  ): Promise<PushSubscription> {
    const subscriptionId = this.generateSubscriptionId();

    const pushSubscription: PushSubscription = {
      id: subscriptionId,
      userId,
      endpoint: subscription.endpoint,
      keys: subscription.keys,
      userAgent,
      createdAt: new Date(),
      lastUsed: new Date(),
      active: true
    };

    this.subscriptions.set(subscriptionId, pushSubscription);

    console.log(`User ${userId} subscribed to push notifications`);

    return pushSubscription;
  }

  /**
   * Unsubscribe user from push notifications
   */
  async unsubscribe(subscriptionId: string): Promise<boolean> {
    const subscription = this.subscriptions.get(subscriptionId);
    if (subscription) {
      subscription.active = false;
      console.log(`Subscription ${subscriptionId} deactivated`);
      return true;
    }
    return false;
  }

  /**
   * Send push notification to specific subscription
   */
  async sendNotification(
    subscriptionId: string,
    notification: PushNotification
  ): Promise<PushMessage> {
    const subscription = this.subscriptions.get(subscriptionId);
    if (!subscription || !subscription.active) {
      throw new Error('Invalid or inactive subscription');
    }

    const messageId = this.generateMessageId();
    const message: PushMessage = {
      id: messageId,
      subscriptionId,
      notification,
      status: 'pending',
    };

    try {
      // In a real implementation, this would use web-push library
      await this.sendToEndpoint(subscription, notification);

      message.status = 'sent';
      message.sentAt = new Date();

      // Simulate delivery confirmation
      setTimeout(() => {
        message.status = 'delivered';
        message.deliveredAt = new Date();
      }, 1000);

      subscription.lastUsed = new Date();

    } catch (error) {
      message.status = 'failed';
      message.errorMessage = (error as Error).message;
    }

    this.messageHistory.set(messageId, message);
    return message;
  }

  /**
   * Send push notification to all user's subscriptions
   */
  async sendToUser(
    userId: string,
    notification: PushNotification
  ): Promise<PushMessage[]> {
    const userSubscriptions = Array.from(this.subscriptions.values())
      .filter(sub => sub.userId === userId && sub.active);

    if (userSubscriptions.length === 0) {
      throw new Error('No active subscriptions found for user');
    }

    const messages = await Promise.all(
      userSubscriptions.map(sub =>
        this.sendNotification(sub.id, notification)
      )
    );

    return messages;
  }

  /**
   * Send templated push notification
   */
  async sendTemplatedNotification(
    userId: string,
    templateId: string,
    variables: Record<string, string>
  ): Promise<PushMessage[]> {
    const template = this.templates.get(templateId);
    if (!template) {
      throw new Error(`Template not found: ${templateId}`);
    }

    let title = template.title;
    let body = template.body;

    // Replace variables in template
    for (const [key, value] of Object.entries(variables)) {
      title = title.replace(new RegExp(`{{${key}}}`, 'g'), value);
      body = body.replace(new RegExp(`{{${key}}}`, 'g'), value);
    }

    const notification: PushNotification = {
      id: this.generateNotificationId(),
      title,
      body,
      icon: template.icon,
      actions: template.actions,
      timestamp: Date.now()
    };

    return this.sendToUser(userId, notification);
  }

  /**
   * Send property alert push notification
   */
  async sendPropertyAlert(
    userId: string,
    propertyData: {
      address: string;
      score: number;
      price: number;
      propertyId: string;
    }
  ): Promise<PushMessage[]> {
    return this.sendTemplatedNotification(
      userId,
      'property_alert',
      {
        address: propertyData.address,
        score: propertyData.score.toString(),
        price: this.formatCurrency(propertyData.price),
        propertyId: propertyData.propertyId
      }
    );
  }

  /**
   * Send report ready notification
   */
  async sendReportReady(
    userId: string,
    reportData: {
      propertyAddress: string;
      reportType: string;
      reportId: string;
    }
  ): Promise<PushMessage[]> {
    return this.sendTemplatedNotification(
      userId,
      'report_ready',
      {
        propertyAddress: reportData.propertyAddress,
        reportType: reportData.reportType,
        reportId: reportData.reportId
      }
    );
  }

  /**
   * Track notification click
   */
  trackClick(messageId: string): void {
    const message = this.messageHistory.get(messageId);
    if (message) {
      message.status = 'clicked';
      message.clickedAt = new Date();
    }
  }

  /**
   * Get user's subscriptions
   */
  getUserSubscriptions(userId: string): PushSubscription[] {
    return Array.from(this.subscriptions.values())
      .filter(sub => sub.userId === userId);
  }

  /**
   * Get push notification analytics
   */
  getAnalytics(startDate?: Date, endDate?: Date): {
    totalSent: number;
    totalDelivered: number;
    totalClicked: number;
    totalFailed: number;
    clickRate: number;
    deliveryRate: number;
    messagesByCategory: Record<string, number>;
    activeSubscriptions: number;
  } {
    const messages = Array.from(this.messageHistory.values());
    const filteredMessages = messages.filter(msg => {
      if (!msg.sentAt) return false;
      if (startDate && msg.sentAt < startDate) return false;
      if (endDate && msg.sentAt > endDate) return false;
      return true;
    });

    const totalSent = filteredMessages.filter(msg => msg.status !== 'failed').length;
    const totalDelivered = filteredMessages.filter(msg => msg.status === 'delivered' || msg.status === 'clicked').length;
    const totalClicked = filteredMessages.filter(msg => msg.status === 'clicked').length;
    const totalFailed = filteredMessages.filter(msg => msg.status === 'failed').length;

    const clickRate = totalDelivered > 0 ? (totalClicked / totalDelivered) * 100 : 0;
    const deliveryRate = totalSent > 0 ? (totalDelivered / totalSent) * 100 : 0;

    const activeSubscriptions = Array.from(this.subscriptions.values())
      .filter(sub => sub.active).length;

    const messagesByCategory: Record<string, number> = {};
    // This would be enhanced to track categories based on templates used

    return {
      totalSent,
      totalDelivered,
      totalClicked,
      totalFailed,
      clickRate,
      deliveryRate,
      messagesByCategory,
      activeSubscriptions
    };
  }

  /**
   * Send to endpoint (mock implementation)
   */
  private async sendToEndpoint(
    subscription: PushSubscription,
    notification: PushNotification
  ): Promise<void> {
    // In a real implementation, this would use the web-push library
    console.log(`Sending push notification to ${subscription.endpoint}:`, {
      title: notification.title,
      body: notification.body
    });

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  /**
   * Format currency for notifications
   */
  private formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  }

  /**
   * Initialize push notification templates
   */
  private initializeTemplates(): void {
    const templates: PushTemplate[] = [
      {
        id: 'property_alert',
        name: 'Property Alert',
        title: '🏠 New Qualified Property',
        body: '{{address}} - Score: {{score}}/100, Price: {{price}}',
        icon: '/icons/property-alert.png',
        actions: [
          { action: 'view', title: 'View Property' },
          { action: 'dismiss', title: 'Dismiss' }
        ],
        variables: ['address', 'score', 'price', 'propertyId'],
        category: 'property_alert'
      },
      {
        id: 'report_ready',
        name: 'Report Ready',
        title: '📊 Report Ready',
        body: 'Your {{reportType}} report for {{propertyAddress}} is ready to view',
        icon: '/icons/report-ready.png',
        actions: [
          { action: 'view', title: 'View Report' },
          { action: 'download', title: 'Download' }
        ],
        variables: ['reportType', 'propertyAddress', 'reportId'],
        category: 'report_ready'
      },
      {
        id: 'system_update',
        name: 'System Update',
        title: '🔧 System Update',
        body: 'System maintenance scheduled for {{date}} at {{time}}',
        icon: '/icons/system-update.png',
        variables: ['date', 'time', 'duration'],
        category: 'system_update'
      },
      {
        id: 'reminder',
        name: 'Reminder',
        title: '⏰ Reminder',
        body: '{{message}}',
        icon: '/icons/reminder.png',
        variables: ['message'],
        category: 'reminder'
      }
    ];

    templates.forEach(template => {
      this.templates.set(template.id, template);
    });
  }

  /**
   * Generate unique IDs
   */
  private generateSubscriptionId(): string {
    return `sub_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  private generateNotificationId(): string {
    return `notif_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }
}