import { Property, EvaluationResults } from '@/types';
import { OpenAIClient } from '@/lib/llm/openai-client';

export interface ReportSection {
  title: string;
  content: string;
  charts?: ChartData[];
  images?: string[];
  tables?: TableData[];
}

export interface ChartData {
  type: 'bar' | 'line' | 'pie' | 'scatter' | 'area';
  title: string;
  data: any[];
  labels: string[];
  options?: any;
}

export interface TableData {
  title: string;
  headers: string[];
  rows: string[][];
}

export interface PropertyReport {
  id: string;
  propertyId: string;
  type: 'executive-summary' | 'detailed-analysis' | 'feasibility-study' | 'comparison';
  title: string;
  generatedAt: Date;
  generatedBy: string;
  sections: ReportSection[];
  metadata: {
    version: string;
    template: string;
    dataSourcesUsed: string[];
    confidenceLevel: number;
  };
}

export class ReportGenerator {
  private openaiClient: OpenAIClient;

  constructor() {
    this.openaiClient = new OpenAIClient();
  }

  /**
   * Generate a comprehensive property report
   */
  async generatePropertyReport(
    property: Property,
    evaluationResults: EvaluationResults,
    reportType: PropertyReport['type'] = 'detailed-analysis',
    userId: string
  ): Promise<PropertyReport> {
    const reportId = this.generateReportId();
    
    const sections = await this.generateReportSections(
      property,
      evaluationResults,
      reportType
    );

    return {
      id: reportId,
      propertyId: property.id,
      type: reportType,
      title: this.generateReportTitle(property, reportType),
      generatedAt: new Date(),
      generatedBy: userId,
      sections,
      metadata: {
        version: '1.0',
        template: reportType,
        dataSourcesUsed: this.getDataSources(evaluationResults),
        confidenceLevel: evaluationResults.confidenceScore,
      },
    };
  }

  /**
   * Generate executive summary report
   */
  async generateExecutiveSummary(
    property: Property,
    evaluationResults: EvaluationResults
  ): Promise<ReportSection> {
    const aiSummary = await this.openaiClient.evaluateProperty({
      propertyData: property,
      criteria: evaluationResults.criteria,
      context: 'Generate an executive summary for real estate investors',
    });

    return {
      title: 'Executive Summary',
      content: this.formatExecutiveSummary(property, evaluationResults, aiSummary),
      charts: [
        this.createScoreChart(evaluationResults),
        this.createCriteriaChart(evaluationResults),
      ],
    };
  }

  /**
   * Generate property information section
   */
  generatePropertyInformation(property: Property): ReportSection {
    const content = `
## Property Overview

**Address:** ${property.address}
**Parcel ID:** ${property.parcelId || 'N/A'}
**Lot Size:** ${this.formatNumber(property.lotSize)} sq ft
**Dimensions:** ${property.dimensions.width}' × ${property.dimensions.length}'
**Zoning:** ${property.zoning}
**Assessed Value:** ${this.formatCurrency(property.assessedValue)}
**Annual Taxes:** ${this.formatCurrency(property.taxAmount)}

## Property Owner

**Name:** ${property.owner.name}
**Address:** ${property.owner.address}
${property.owner.phone ? `**Phone:** ${property.owner.phone}` : ''}
${property.owner.email ? `**Email:** ${property.owner.email}` : ''}
`;

    return {
      title: 'Property Information',
      content,
      tables: [
        {
          title: 'Property Details',
          headers: ['Attribute', 'Value'],
          rows: [
            ['Address', property.address],
            ['Parcel ID', property.parcelId || 'N/A'],
            ['Lot Size', `${this.formatNumber(property.lotSize)} sq ft`],
            ['Zoning', property.zoning],
            ['Assessed Value', this.formatCurrency(property.assessedValue)],
            ['Annual Taxes', this.formatCurrency(property.taxAmount)],
          ],
        },
      ],
    };
  }

  /**
   * Generate location analysis section
   */
  async generateLocationAnalysis(
    property: Property,
    evaluationResults: EvaluationResults
  ): Promise<ReportSection> {
    const transitAnalysis = evaluationResults.criteria.transitAccess;
    
    const content = `
## Location Analysis

### Transit Accessibility
- **Nearest Transit Stop:** ${transitAnalysis.nearestStopDistance.toFixed(2)} miles
- **Within Quarter Mile:** ${transitAnalysis.withinQuarterMile ? 'Yes' : 'No'}
- **Peak Service Frequency:** Every ${transitAnalysis.peakFrequency} minutes
- **Off-Peak Service Frequency:** Every ${transitAnalysis.offPeakFrequency} minutes
- **Transit Eligibility:** ${transitAnalysis.isEligible ? 'Qualified' : 'Not Qualified'}

### Neighborhood Characteristics
- **QCT/DDA Status:** ${evaluationResults.criteria.qctDdaStatus ? 'Qualified' : 'Not Qualified'}
- **Neighborhood Change Zone:** ${evaluationResults.criteria.neighborhoodChangeZone ? 'Yes' : 'No'}
- **Historical Designation:** ${evaluationResults.criteria.historicalStatus.hasDesignation ? 'Yes' : 'No'}
- **Environmental Issues:** ${evaluationResults.criteria.environmentalConcerns.hasIssues ? 'Yes' : 'No'}
`;

    return {
      title: 'Location Analysis',
      content,
      charts: [
        this.createTransitAccessibilityChart(transitAnalysis),
      ],
    };
  }

  /**
   * Generate zoning analysis section
   */
  async generateZoningAnalysis(
    property: Property,
    evaluationResults: EvaluationResults
  ): Promise<ReportSection> {
    const densityAnalysis = evaluationResults.criteria.densityPotential;
    const heightAnalysis = evaluationResults.criteria.heightRestrictions;
    const lotAnalysis = evaluationResults.criteria.lotSizeAnalysis;
    
    const content = `
## Zoning Analysis

### Development Potential
- **Base Units:** ${densityAnalysis.baseUnits}
- **Bonus Units:** ${densityAnalysis.bonusUnits}
- **Total Units:** ${densityAnalysis.totalUnits}
- **Meets 250+ Requirement:** ${densityAnalysis.meets250Requirement ? 'Yes' : 'No'}

### Height Restrictions
- **Base Height Allowance:** ${heightAnalysis.baseHeight} ft
- **Bonus Height:** ${heightAnalysis.bonusHeight} ft
- **Total Height Potential:** ${heightAnalysis.totalHeight} ft
- **Meets 65ft Requirement:** ${heightAnalysis.meets65FtRequirement ? 'Yes' : 'No'}

### Lot Analysis
- **Lot Size:** ${this.formatNumber(lotAnalysis.size)} sq ft
- **Size Category:** ${lotAnalysis.isIdeal ? 'Ideal' : lotAnalysis.isAcceptable ? 'Acceptable' : 'Too Large'}
- **Subdivision Potential:** ${lotAnalysis.subdivisionPotential ? 'Yes' : 'No'}
- **Minimum Width:** ${lotAnalysis.minimumWidth} ft
- **Width Requirement Met:** ${lotAnalysis.meetsWidthRequirement ? 'Yes' : 'No'}
`;

    return {
      title: 'Zoning Analysis',
      content,
      charts: [
        this.createDensityChart(densityAnalysis),
        this.createHeightChart(heightAnalysis),
      ],
    };
  }

  /**
   * Generate financial feasibility section
   */
  async generateFinancialAnalysis(
    property: Property,
    evaluationResults: EvaluationResults
  ): Promise<ReportSection> {
    // This would integrate with financial modeling
    const estimatedDevelopmentCost = this.estimateDevelopmentCost(property, evaluationResults);
    const projectedRevenue = this.estimateRevenue(property, evaluationResults);
    const roi = ((projectedRevenue - estimatedDevelopmentCost) / estimatedDevelopmentCost) * 100;

    const content = `
## Financial Feasibility Analysis

### Cost Estimates
- **Land Acquisition:** ${this.formatCurrency(property.assessedValue)}
- **Development Cost:** ${this.formatCurrency(estimatedDevelopmentCost)}
- **Total Investment:** ${this.formatCurrency(property.assessedValue + estimatedDevelopmentCost)}

### Revenue Projections
- **Projected Revenue:** ${this.formatCurrency(projectedRevenue)}
- **Net Profit:** ${this.formatCurrency(projectedRevenue - property.assessedValue - estimatedDevelopmentCost)}
- **ROI:** ${roi.toFixed(1)}%

### Key Assumptions
- Construction cost: $300/sq ft
- Average unit size: 800 sq ft
- Average sale price: $600,000/unit
- Development timeline: 24-36 months
`;

    return {
      title: 'Financial Analysis',
      content,
      charts: [
        this.createFinancialChart(property, estimatedDevelopmentCost, projectedRevenue),
      ],
    };
  }

  /**
   * Generate risk assessment section
   */
  generateRiskAssessment(evaluationResults: EvaluationResults): ReportSection {
    const risks: string[] = [];
    const mitigations: string[] = [];

    // Analyze risks based on evaluation results
    if (!evaluationResults.criteria.qctDdaStatus) {
      risks.push('Property not in QCT/DDA area - limits density bonus eligibility');
      mitigations.push('Explore alternative density bonus programs');
    }

    if (!evaluationResults.criteria.transitAccess.isEligible) {
      risks.push('Limited transit access may affect project viability');
      mitigations.push('Consider shuttle service or improved pedestrian access');
    }

    if (evaluationResults.criteria.environmentalConcerns.hasIssues) {
      risks.push('Environmental issues may require costly remediation');
      mitigations.push('Conduct Phase II environmental assessment');
    }

    const content = `
## Risk Assessment

### Identified Risks
${risks.map(risk => `- ${risk}`).join('\n')}

### Mitigation Strategies
${mitigations.map(mitigation => `- ${mitigation}`).join('\n')}

### Overall Risk Level
**${this.calculateRiskLevel(evaluationResults)}**

Based on the evaluation criteria, this property presents a ${this.calculateRiskLevel(evaluationResults).toLowerCase()} risk profile for development.
`;

    return {
      title: 'Risk Assessment',
      content,
    };
  }

  private async generateReportSections(
    property: Property,
    evaluationResults: EvaluationResults,
    reportType: PropertyReport['type']
  ): Promise<ReportSection[]> {
    const sections: ReportSection[] = [];

    // Always include executive summary
    sections.push(await this.generateExecutiveSummary(property, evaluationResults));

    if (reportType === 'detailed-analysis' || reportType === 'feasibility-study') {
      sections.push(this.generatePropertyInformation(property));
      sections.push(await this.generateLocationAnalysis(property, evaluationResults));
      sections.push(await this.generateZoningAnalysis(property, evaluationResults));
      sections.push(await this.generateFinancialAnalysis(property, evaluationResults));
      sections.push(this.generateRiskAssessment(evaluationResults));
    }

    return sections;
  }

  private formatExecutiveSummary(
    property: Property,
    evaluationResults: EvaluationResults,
    aiSummary: any
  ): string {
    return `
## Executive Summary

**Property:** ${property.address}
**Overall Score:** ${evaluationResults.overallScore}/100
**Recommendation:** ${evaluationResults.passed ? 'PROCEED' : 'DO NOT PROCEED'}

### Key Findings
${aiSummary.strengths.map((strength: string) => `- ${strength}`).join('\n')}

### Development Potential
${aiSummary.developmentPotential}

### Financial Outlook
- **Estimated Units:** ${evaluationResults.criteria.densityPotential.totalUnits}
- **Investment Required:** ${this.formatCurrency(property.assessedValue + this.estimateDevelopmentCost(property, evaluationResults))}
- **Projected ROI:** ${this.calculateROI(property, evaluationResults).toFixed(1)}%

### Next Steps
${evaluationResults.recommendations.map(rec => `- ${rec}`).join('\n')}
`;
  }

  // Helper methods for calculations and formatting
  private estimateDevelopmentCost(property: Property, evaluationResults: EvaluationResults): number {
    const units = evaluationResults.criteria.densityPotential.totalUnits;
    const avgUnitSize = 800; // sq ft
    const costPerSqFt = 300; // $300/sq ft
    return units * avgUnitSize * costPerSqFt;
  }

  private estimateRevenue(property: Property, evaluationResults: EvaluationResults): number {
    const units = evaluationResults.criteria.densityPotential.totalUnits;
    const avgSalePrice = 600000; // $600k per unit
    return units * avgSalePrice;
  }

  private calculateROI(property: Property, evaluationResults: EvaluationResults): number {
    const totalCost = property.assessedValue + this.estimateDevelopmentCost(property, evaluationResults);
    const revenue = this.estimateRevenue(property, evaluationResults);
    return ((revenue - totalCost) / totalCost) * 100;
  }

  private calculateRiskLevel(evaluationResults: EvaluationResults): string {
    const score = evaluationResults.overallScore;
    if (score >= 80) return 'Low';
    if (score >= 60) return 'Medium';
    return 'High';
  }

  private createScoreChart(evaluationResults: EvaluationResults): ChartData {
    return {
      type: 'bar',
      title: 'Overall Evaluation Score',
      data: [evaluationResults.overallScore],
      labels: ['Score'],
    };
  }

  private createCriteriaChart(evaluationResults: EvaluationResults): ChartData {
    const criteria = evaluationResults.criteria;
    return {
      type: 'bar',
      title: 'Criteria Evaluation',
      data: [
        criteria.qctDdaStatus ? 100 : 0,
        criteria.neighborhoodChangeZone ? 100 : 0,
        criteria.lotSizeAnalysis.isIdeal ? 100 : criteria.lotSizeAnalysis.isAcceptable ? 70 : 0,
        criteria.densityPotential.meets250Requirement ? 100 : 0,
        criteria.heightRestrictions.meets65FtRequirement ? 100 : 0,
        criteria.transitAccess.isEligible ? 100 : 0,
      ],
      labels: ['QCT/DDA', 'Change Zone', 'Lot Size', 'Density', 'Height', 'Transit'],
    };
  }

  private createTransitAccessibilityChart(transitAnalysis: any): ChartData {
    return {
      type: 'bar',
      title: 'Transit Service Frequency',
      data: [transitAnalysis.peakFrequency, transitAnalysis.offPeakFrequency],
      labels: ['Peak Hours', 'Off-Peak Hours'],
    };
  }

  private createDensityChart(densityAnalysis: any): ChartData {
    return {
      type: 'pie',
      title: 'Unit Distribution',
      data: [densityAnalysis.baseUnits, densityAnalysis.bonusUnits],
      labels: ['Base Units', 'Bonus Units'],
    };
  }

  private createHeightChart(heightAnalysis: any): ChartData {
    return {
      type: 'bar',
      title: 'Height Analysis',
      data: [heightAnalysis.baseHeight, heightAnalysis.bonusHeight],
      labels: ['Base Height', 'Bonus Height'],
    };
  }

  private createFinancialChart(property: Property, developmentCost: number, revenue: number): ChartData {
    return {
      type: 'bar',
      title: 'Financial Overview',
      data: [property.assessedValue, developmentCost, revenue],
      labels: ['Land Cost', 'Development Cost', 'Projected Revenue'],
    };
  }

  private generateReportId(): string {
    return `report_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  private generateReportTitle(property: Property, reportType: PropertyReport['type']): string {
    const typeNames = {
      'executive-summary': 'Executive Summary',
      'detailed-analysis': 'Detailed Property Analysis',
      'feasibility-study': 'Development Feasibility Study',
      'comparison': 'Property Comparison Report',
    };
    
    return `${typeNames[reportType]} - ${property.address}`;
  }

  private getDataSources(evaluationResults: EvaluationResults): string[] {
    return [
      'County Assessor Records',
      'Zoning Department',
      'Transit Authority',
      'Environmental Database',
      'Census Data',
      'HUD QCT/DDA Database',
    ];
  }

  private formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(amount);
  }

  private formatNumber(num: number): string {
    return new Intl.NumberFormat('en-US').format(num);
  }
}
