export interface AnalyticsEvent {
  id: string;
  userId: string;
  sessionId: string;
  event: string;
  category: 'property' | 'search' | 'report' | 'user' | 'system';
  properties: Record<string, any>;
  timestamp: Date;
  userAgent?: string;
  ipAddress?: string;
}

export interface UserMetrics {
  userId: string;
  totalSearches: number;
  totalProperties: number;
  totalReports: number;
  averageScore: number;
  lastActivity: Date;
  timeSpent: number; // in minutes
  conversionRate: number; // properties evaluated / properties viewed
}

export interface PropertyMetrics {
  propertyId: string;
  views: number;
  evaluations: number;
  reports: number;
  averageScore: number;
  lastViewed: Date;
  conversionRate: number;
}

export interface SearchMetrics {
  query: string;
  location: string;
  filters: Record<string, any>;
  resultsCount: number;
  clickThroughRate: number;
  timestamp: Date;
  userId: string;
}

export interface SystemMetrics {
  totalUsers: number;
  activeUsers: number;
  totalProperties: number;
  totalSearches: number;
  totalReports: number;
  averageResponseTime: number;
  errorRate: number;
  uptime: number;
}

export class AnalyticsService {
  private events: Map<string, AnalyticsEvent> = new Map();
  private userMetrics: Map<string, UserMetrics> = new Map();
  private propertyMetrics: Map<string, PropertyMetrics> = new Map();
  private searchMetrics: SearchMetrics[] = [];

  async trackEvent(
    userId: string,
    sessionId: string,
    event: string,
    category: AnalyticsEvent['category'],
    properties: Record<string, any> = {},
    userAgent?: string,
    ipAddress?: string
  ): Promise<string> {
    const id = `event_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    
    const analyticsEvent: AnalyticsEvent = {
      id,
      userId,
      sessionId,
      event,
      category,
      properties,
      timestamp: new Date(),
      userAgent,
      ipAddress,
    };

    this.events.set(id, analyticsEvent);
    
    // Update relevant metrics
    await this.updateMetrics(analyticsEvent);
    
    // In production, send to analytics service (Google Analytics, Mixpanel, etc.)
    console.log('Analytics event tracked:', {
      event,
      category,
      userId,
      properties: Object.keys(properties),
    });

    return id;
  }

  private async updateMetrics(event: AnalyticsEvent) {
    // Update user metrics
    this.updateUserMetrics(event);
    
    // Update property metrics if applicable
    if (event.properties.propertyId) {
      this.updatePropertyMetrics(event);
    }
    
    // Update search metrics if applicable
    if (event.category === 'search') {
      this.updateSearchMetrics(event);
    }
  }

  private updateUserMetrics(event: AnalyticsEvent) {
    let metrics = this.userMetrics.get(event.userId);
    
    if (!metrics) {
      metrics = {
        userId: event.userId,
        totalSearches: 0,
        totalProperties: 0,
        totalReports: 0,
        averageScore: 0,
        lastActivity: new Date(),
        timeSpent: 0,
        conversionRate: 0,
      };
    }

    metrics.lastActivity = event.timestamp;

    switch (event.event) {
      case 'property_search':
        metrics.totalSearches++;
        break;
      case 'property_view':
        metrics.totalProperties++;
        break;
      case 'report_generate':
        metrics.totalReports++;
        break;
      case 'session_duration':
        metrics.timeSpent += event.properties.duration || 0;
        break;
    }

    // Calculate conversion rate
    if (metrics.totalProperties > 0) {
      metrics.conversionRate = metrics.totalReports / metrics.totalProperties;
    }

    this.userMetrics.set(event.userId, metrics);
  }

  private updatePropertyMetrics(event: AnalyticsEvent) {
    const propertyId = event.properties.propertyId;
    let metrics = this.propertyMetrics.get(propertyId);
    
    if (!metrics) {
      metrics = {
        propertyId,
        views: 0,
        evaluations: 0,
        reports: 0,
        averageScore: 0,
        lastViewed: new Date(),
        conversionRate: 0,
      };
    }

    metrics.lastViewed = event.timestamp;

    switch (event.event) {
      case 'property_view':
        metrics.views++;
        break;
      case 'property_evaluate':
        metrics.evaluations++;
        if (event.properties.score) {
          metrics.averageScore = (metrics.averageScore + event.properties.score) / 2;
        }
        break;
      case 'report_generate':
        metrics.reports++;
        break;
    }

    // Calculate conversion rate
    if (metrics.views > 0) {
      metrics.conversionRate = metrics.reports / metrics.views;
    }

    this.propertyMetrics.set(propertyId, metrics);
  }

  private updateSearchMetrics(event: AnalyticsEvent) {
    if (event.event === 'property_search') {
      const searchMetric: SearchMetrics = {
        query: event.properties.query || '',
        location: event.properties.location || '',
        filters: event.properties.filters || {},
        resultsCount: event.properties.resultsCount || 0,
        clickThroughRate: 0, // Will be calculated later
        timestamp: event.timestamp,
        userId: event.userId,
      };
      
      this.searchMetrics.push(searchMetric);
    }
  }

  getUserMetrics(userId: string): UserMetrics | undefined {
    return this.userMetrics.get(userId);
  }

  getPropertyMetrics(propertyId: string): PropertyMetrics | undefined {
    return this.propertyMetrics.get(propertyId);
  }

  getTopProperties(limit: number = 10): PropertyMetrics[] {
    return Array.from(this.propertyMetrics.values())
      .sort((a, b) => b.views - a.views)
      .slice(0, limit);
  }

  getTopSearches(limit: number = 10): { query: string; count: number }[] {
    const searchCounts = new Map<string, number>();
    
    for (const search of this.searchMetrics) {
      const count = searchCounts.get(search.query) || 0;
      searchCounts.set(search.query, count + 1);
    }
    
    return Array.from(searchCounts.entries())
      .map(([query, count]) => ({ query, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);
  }

  getActiveUsers(timeframe: 'day' | 'week' | 'month' = 'day'): number {
    const now = new Date();
    const cutoff = new Date();
    
    switch (timeframe) {
      case 'day':
        cutoff.setDate(now.getDate() - 1);
        break;
      case 'week':
        cutoff.setDate(now.getDate() - 7);
        break;
      case 'month':
        cutoff.setMonth(now.getMonth() - 1);
        break;
    }

    const activeUserIds = new Set<string>();
    
    for (const event of this.events.values()) {
      if (event.timestamp >= cutoff) {
        activeUserIds.add(event.userId);
      }
    }
    
    return activeUserIds.size;
  }

  getSystemMetrics(): SystemMetrics {
    const totalUsers = this.userMetrics.size;
    const activeUsers = this.getActiveUsers('day');
    const totalProperties = this.propertyMetrics.size;
    const totalSearches = this.searchMetrics.length;
    const totalReports = Array.from(this.userMetrics.values())
      .reduce((sum, metrics) => sum + metrics.totalReports, 0);

    // Mock system metrics
    return {
      totalUsers,
      activeUsers,
      totalProperties,
      totalSearches,
      totalReports,
      averageResponseTime: 250, // ms
      errorRate: 0.02, // 2%
      uptime: 99.9, // 99.9%
    };
  }

  getEventsByCategory(
    category: AnalyticsEvent['category'],
    timeframe?: { start: Date; end: Date }
  ): AnalyticsEvent[] {
    let events = Array.from(this.events.values()).filter(e => e.category === category);
    
    if (timeframe) {
      events = events.filter(e => 
        e.timestamp >= timeframe.start && e.timestamp <= timeframe.end
      );
    }
    
    return events.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  getConversionFunnel(): {
    searches: number;
    propertyViews: number;
    evaluations: number;
    reports: number;
  } {
    const searches = this.searchMetrics.length;
    const propertyViews = Array.from(this.events.values())
      .filter(e => e.event === 'property_view').length;
    const evaluations = Array.from(this.events.values())
      .filter(e => e.event === 'property_evaluate').length;
    const reports = Array.from(this.events.values())
      .filter(e => e.event === 'report_generate').length;

    return { searches, propertyViews, evaluations, reports };
  }

  getUserJourney(userId: string): AnalyticsEvent[] {
    return Array.from(this.events.values())
      .filter(e => e.userId === userId)
      .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  }

  getPerformanceMetrics(timeframe: { start: Date; end: Date }): {
    averageSearchTime: number;
    averageEvaluationTime: number;
    averageReportTime: number;
    errorRate: number;
  } {
    const events = Array.from(this.events.values()).filter(e =>
      e.timestamp >= timeframe.start && e.timestamp <= timeframe.end
    );

    const searchTimes = events
      .filter(e => e.event === 'property_search' && e.properties.duration)
      .map(e => e.properties.duration);
    
    const evaluationTimes = events
      .filter(e => e.event === 'property_evaluate' && e.properties.duration)
      .map(e => e.properties.duration);
    
    const reportTimes = events
      .filter(e => e.event === 'report_generate' && e.properties.duration)
      .map(e => e.properties.duration);

    const errors = events.filter(e => e.event === 'error').length;
    const total = events.length;

    return {
      averageSearchTime: searchTimes.length > 0 
        ? searchTimes.reduce((a, b) => a + b, 0) / searchTimes.length 
        : 0,
      averageEvaluationTime: evaluationTimes.length > 0
        ? evaluationTimes.reduce((a, b) => a + b, 0) / evaluationTimes.length
        : 0,
      averageReportTime: reportTimes.length > 0
        ? reportTimes.reduce((a, b) => a + b, 0) / reportTimes.length
        : 0,
      errorRate: total > 0 ? errors / total : 0,
    };
  }

  generateInsights(): {
    topPerformingProperties: PropertyMetrics[];
    userEngagementTrends: any[];
    searchPatterns: any[];
    conversionOptimization: string[];
  } {
    const topPerformingProperties = this.getTopProperties(5);
    
    // Mock insights - in production, these would be more sophisticated
    const userEngagementTrends = [
      { date: '2024-01-01', activeUsers: 45, searches: 120, reports: 23 },
      { date: '2024-01-02', activeUsers: 52, searches: 135, reports: 28 },
      { date: '2024-01-03', activeUsers: 48, searches: 128, reports: 25 },
    ];

    const searchPatterns = this.getTopSearches(5);

    const conversionOptimization = [
      'Improve property detail pages to increase evaluation rate',
      'Simplify report generation process',
      'Add more filtering options to search',
      'Implement property comparison feature',
    ];

    return {
      topPerformingProperties,
      userEngagementTrends,
      searchPatterns,
      conversionOptimization,
    };
  }

  // Export data for external analytics tools
  exportData(format: 'json' | 'csv' = 'json'): string {
    const data = {
      events: Array.from(this.events.values()),
      userMetrics: Array.from(this.userMetrics.values()),
      propertyMetrics: Array.from(this.propertyMetrics.values()),
      searchMetrics: this.searchMetrics,
    };

    if (format === 'json') {
      return JSON.stringify(data, null, 2);
    } else {
      // Simple CSV export for events
      const headers = ['id', 'userId', 'event', 'category', 'timestamp'];
      const rows = data.events.map(event => [
        event.id,
        event.userId,
        event.event,
        event.category,
        event.timestamp.toISOString(),
      ]);
      
      return [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
    }
  }
}
