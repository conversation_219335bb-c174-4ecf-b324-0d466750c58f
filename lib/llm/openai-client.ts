import OpenAI from 'openai';

export interface DocumentAnalysisRequest {
  documentType: 'zoning-code' | 'building-code' | 'environmental-report' | 'transit-schedule' | 'master-plan';
  content: string;
  propertyAddress?: string;
  specificQuestions?: string[];
}

export interface DocumentAnalysisResponse {
  summary: string;
  keyFindings: string[];
  requirements: string[];
  restrictions: string[];
  opportunities: string[];
  risks: string[];
  recommendations: string[];
  confidence: number;
  sources: string[];
}

export interface PropertyEvaluationRequest {
  propertyData: any;
  criteria: any;
  context?: string;
}

export interface PropertyEvaluationResponse {
  overallAssessment: string;
  strengths: string[];
  weaknesses: string[];
  developmentPotential: string;
  financialProjection: {
    estimatedCost: number;
    projectedRevenue: number;
    roi: number;
    timeline: string;
  };
  riskAssessment: {
    level: 'low' | 'medium' | 'high';
    factors: string[];
    mitigation: string[];
  };
  nextSteps: string[];
}

export class OpenAIClient {
  private client: OpenAI;
  private model: string;

  constructor(apiKey?: string, model: string = 'gpt-4') {
    this.client = new OpenAI({
      apiKey: apiKey || process.env.OPENAI_API_KEY,
    });
    this.model = model;
  }

  /**
   * Analyze documents using GPT-4
   */
  async analyzeDocument(request: DocumentAnalysisRequest): Promise<DocumentAnalysisResponse> {
    const prompt = this.buildDocumentAnalysisPrompt(request);
    
    try {
      const response = await this.client.chat.completions.create({
        model: this.model,
        messages: [
          {
            role: 'system',
            content: this.getSystemPrompt(request.documentType),
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: 0.1,
        max_tokens: 2000,
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No response from OpenAI');
      }

      return this.parseDocumentAnalysisResponse(content);
    } catch (error) {
      console.error('OpenAI API error:', error);
      throw new Error(`Document analysis failed: ${(error as Error).message}`);
    }
  }

  /**
   * Evaluate property using AI
   */
  async evaluateProperty(request: PropertyEvaluationRequest): Promise<PropertyEvaluationResponse> {
    const prompt = this.buildPropertyEvaluationPrompt(request);
    
    try {
      const response = await this.client.chat.completions.create({
        model: this.model,
        messages: [
          {
            role: 'system',
            content: this.getPropertyEvaluationSystemPrompt(),
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: 0.2,
        max_tokens: 3000,
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No response from OpenAI');
      }

      return this.parsePropertyEvaluationResponse(content);
    } catch (error) {
      console.error('OpenAI API error:', error);
      throw new Error(`Property evaluation failed: ${(error as Error).message}`);
    }
  }

  /**
   * Generate recommendations based on evaluation results
   */
  async generateRecommendations(
    propertyData: any,
    evaluationResults: any,
    userPreferences?: any
  ): Promise<string[]> {
    const prompt = `
Based on the following property data and evaluation results, generate specific, actionable recommendations for a real estate developer:

Property Data:
${JSON.stringify(propertyData, null, 2)}

Evaluation Results:
${JSON.stringify(evaluationResults, null, 2)}

User Preferences:
${userPreferences ? JSON.stringify(userPreferences, null, 2) : 'None specified'}

Please provide 5-10 specific, actionable recommendations that would help maximize the development potential of this property.
`;

    try {
      const response = await this.client.chat.completions.create({
        model: this.model,
        messages: [
          {
            role: 'system',
            content: 'You are an expert real estate development consultant. Provide specific, actionable recommendations based on property analysis.',
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: 0.3,
        max_tokens: 1500,
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No response from OpenAI');
      }

      // Parse recommendations from response
      const lines = content.split('\n').filter(line => line.trim());
      return lines
        .filter(line => line.match(/^\d+\./) || line.startsWith('-') || line.startsWith('•'))
        .map(line => line.replace(/^\d+\.\s*/, '').replace(/^[-•]\s*/, '').trim())
        .filter(line => line.length > 0);
    } catch (error) {
      console.error('OpenAI API error:', error);
      throw new Error(`Recommendation generation failed: ${(error as Error).message}`);
    }
  }

  private buildDocumentAnalysisPrompt(request: DocumentAnalysisRequest): string {
    let prompt = `Please analyze the following ${request.documentType} document`;
    
    if (request.propertyAddress) {
      prompt += ` for property at ${request.propertyAddress}`;
    }
    
    prompt += ':\n\n';
    prompt += request.content;
    
    if (request.specificQuestions && request.specificQuestions.length > 0) {
      prompt += '\n\nPlease specifically address these questions:\n';
      request.specificQuestions.forEach((question, index) => {
        prompt += `${index + 1}. ${question}\n`;
      });
    }
    
    prompt += '\n\nPlease provide a comprehensive analysis including summary, key findings, requirements, restrictions, opportunities, risks, and recommendations.';
    
    return prompt;
  }

  private buildPropertyEvaluationPrompt(request: PropertyEvaluationRequest): string {
    let prompt = 'Please evaluate the following property for real estate development potential:\n\n';
    
    prompt += 'Property Data:\n';
    prompt += JSON.stringify(request.propertyData, null, 2);
    
    prompt += '\n\nEvaluation Criteria:\n';
    prompt += JSON.stringify(request.criteria, null, 2);
    
    if (request.context) {
      prompt += '\n\nAdditional Context:\n';
      prompt += request.context;
    }
    
    prompt += '\n\nPlease provide a comprehensive evaluation including overall assessment, strengths, weaknesses, development potential, financial projections, risk assessment, and next steps.';
    
    return prompt;
  }

  private getSystemPrompt(documentType: string): string {
    const prompts = {
      'zoning-code': 'You are an expert zoning analyst. Analyze zoning documents to extract key requirements, restrictions, and development opportunities.',
      'building-code': 'You are an expert building code analyst. Analyze building codes to identify requirements, restrictions, and compliance considerations.',
      'environmental-report': 'You are an environmental consultant. Analyze environmental reports to identify risks, requirements, and mitigation strategies.',
      'transit-schedule': 'You are a transportation planner. Analyze transit schedules to determine accessibility and frequency of service.',
      'master-plan': 'You are an urban planning expert. Analyze master plans to understand development vision, policies, and opportunities.',
    };
    
    return prompts[documentType as keyof typeof prompts] || 'You are an expert document analyst.';
  }

  private getPropertyEvaluationSystemPrompt(): string {
    return `You are an expert real estate development consultant with deep knowledge of:
- Zoning regulations and density bonuses
- Development feasibility analysis
- Financial modeling for real estate projects
- Risk assessment and mitigation
- Market analysis and trends
- Construction costs and timelines

Provide comprehensive, data-driven evaluations that help developers make informed investment decisions.`;
  }

  private parseDocumentAnalysisResponse(content: string): DocumentAnalysisResponse {
    // This is a simplified parser - in production, you'd want more robust parsing
    const sections = this.extractSections(content);
    
    return {
      summary: sections.summary || '',
      keyFindings: this.extractListItems(sections.keyFindings || sections.findings || ''),
      requirements: this.extractListItems(sections.requirements || ''),
      restrictions: this.extractListItems(sections.restrictions || ''),
      opportunities: this.extractListItems(sections.opportunities || ''),
      risks: this.extractListItems(sections.risks || ''),
      recommendations: this.extractListItems(sections.recommendations || ''),
      confidence: 0.85, // Default confidence - could be extracted from response
      sources: [], // Would be populated if document contained source references
    };
  }

  private parsePropertyEvaluationResponse(content: string): PropertyEvaluationResponse {
    const sections = this.extractSections(content);
    
    return {
      overallAssessment: sections.assessment || sections.summary || '',
      strengths: this.extractListItems(sections.strengths || ''),
      weaknesses: this.extractListItems(sections.weaknesses || ''),
      developmentPotential: sections.potential || sections.development || '',
      financialProjection: {
        estimatedCost: 0, // Would be extracted from response
        projectedRevenue: 0,
        roi: 0,
        timeline: sections.timeline || '',
      },
      riskAssessment: {
        level: 'medium' as const,
        factors: this.extractListItems(sections.risks || ''),
        mitigation: this.extractListItems(sections.mitigation || ''),
      },
      nextSteps: this.extractListItems(sections.nextSteps || sections.steps || ''),
    };
  }

  private extractSections(content: string): { [key: string]: string } {
    const sections: { [key: string]: string } = {};
    const lines = content.split('\n');
    let currentSection = '';
    let currentContent = '';
    
    for (const line of lines) {
      const trimmed = line.trim();
      
      // Check if this is a section header
      if (trimmed.match(/^(summary|key findings|findings|requirements|restrictions|opportunities|risks|recommendations|assessment|strengths|weaknesses|potential|development|timeline|next steps|steps|mitigation):/i)) {
        // Save previous section
        if (currentSection) {
          sections[currentSection] = currentContent.trim();
        }
        
        // Start new section
        currentSection = trimmed.replace(':', '').toLowerCase().replace(/\s+/g, '');
        currentContent = '';
      } else if (currentSection) {
        currentContent += line + '\n';
      }
    }
    
    // Save last section
    if (currentSection) {
      sections[currentSection] = currentContent.trim();
    }
    
    return sections;
  }

  private extractListItems(content: string): string[] {
    const lines = content.split('\n');
    return lines
      .filter(line => line.trim().match(/^[-•*]\s+/) || line.trim().match(/^\d+\.\s+/))
      .map(line => line.replace(/^[-•*]\s+/, '').replace(/^\d+\.\s+/, '').trim())
      .filter(line => line.length > 0);
  }
}
