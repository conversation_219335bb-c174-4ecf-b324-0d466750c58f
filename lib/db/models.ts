import mongoose, { Schema, Document } from 'mongoose';
import { User, Property, SavedSearch, PropertyReport } from '@/types';

// User Schema
interface UserDocument extends Omit<User, 'id'>, Document {
  password?: string; // Optional for OAuth users
}

const UserSchema = new Schema<UserDocument>({
  email: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  password: { type: String }, // Optional for OAuth users
  role: { type: String, enum: ['admin', 'user'], default: 'user' },
}, {
  timestamps: true,
});

// Property Schema
interface PropertyDocument extends Omit<Property, 'id'>, Document {}

const PropertySchema = new Schema<PropertyDocument>({
  address: { type: String, required: true },
  parcelId: { type: String },
  coordinates: {
    lat: { type: Number, required: true },
    lng: { type: Number, required: true },
  },
  lotSize: { type: Number, required: true },
  dimensions: {
    width: { type: Number, required: true },
    length: { type: Number, required: true },
  },
  zoning: { type: String, required: true },
  assessedValue: { type: Number, required: true },
  marketValue: { type: Number },
  taxAmount: { type: Number, required: true },
  owner: {
    name: { type: String, required: true },
    address: { type: String, required: true },
    phone: { type: String },
    email: { type: String },
  },
  evaluationResults: {
    overallScore: { type: Number },
    passed: { type: Boolean },
    criteria: { type: Schema.Types.Mixed },
    recommendations: [{ type: String }],
    alternativeSuggestions: [{ type: String }],
    confidenceScore: { type: Number },
    evaluatedAt: { type: Date },
  },
}, {
  timestamps: true,
});

// Saved Search Schema
interface SavedSearchDocument extends Omit<SavedSearch, 'id'>, Document {}

const SavedSearchSchema = new Schema<SavedSearchDocument>({
  userId: { type: String, required: true },
  name: { type: String, required: true },
  criteria: {
    location: { type: String },
    radius: { type: Number },
    minLotSize: { type: Number },
    maxLotSize: { type: Number },
    minPrice: { type: Number },
    maxPrice: { type: Number },
    zoning: [{ type: String }],
    requiresQctDda: { type: Boolean },
  },
  results: [{ type: Schema.Types.ObjectId, ref: 'Property' }],
}, {
  timestamps: true,
});

// Property Report Schema
interface PropertyReportDocument extends Omit<PropertyReport, 'id'>, Document {}

const PropertyReportSchema = new Schema<PropertyReportDocument>({
  propertyId: { type: String, required: true },
  type: { type: String, enum: ['summary', 'detailed', 'comparison'], required: true },
  sections: [{
    title: { type: String, required: true },
    content: { type: String, required: true },
    charts: [{ type: Schema.Types.Mixed }],
    images: [{ type: String }],
  }],
  generatedBy: { type: String, required: true },
}, {
  timestamps: true,
});

// Create indexes
PropertySchema.index({ coordinates: '2dsphere' });
PropertySchema.index({ address: 'text' });
PropertySchema.index({ lotSize: 1 });
PropertySchema.index({ assessedValue: 1 });

SavedSearchSchema.index({ userId: 1 });
SavedSearchSchema.index({ createdAt: -1 });

PropertyReportSchema.index({ propertyId: 1 });
PropertyReportSchema.index({ generatedAt: -1 });

// Export models
export const UserModel = mongoose.models.User || mongoose.model<UserDocument>('User', UserSchema);
export const PropertyModel = mongoose.models.Property || mongoose.model<PropertyDocument>('Property', PropertySchema);
export const SavedSearchModel = mongoose.models.SavedSearch || mongoose.model<SavedSearchDocument>('SavedSearch', SavedSearchSchema);
export const PropertyReportModel = mongoose.models.PropertyReport || mongoose.model<PropertyReportDocument>('PropertyReport', PropertyReportSchema);

// Connect to MongoDB
export async function connectToDatabase() {
  if (mongoose.connection.readyState >= 1) {
    return;
  }

  try {
    await mongoose.connect(process.env.MONGODB_URI!);
    console.log('Connected to MongoDB');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    throw error;
  }
}
