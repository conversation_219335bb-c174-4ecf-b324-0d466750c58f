import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { ReportSharingService } from '@/lib/reports/report-sharing';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { 
      reportId,
      shareType = 'private',
      recipients = [],
      expiresAt,
      password,
      permissions = {
        canView: true,
        canComment: false,
        canDownload: false,
        canShare: false,
        canEdit: false
      },
      allowDownload = false,
      allowComments = false,
      trackViews = true
    } = body;

    if (!reportId) {
      return NextResponse.json(
        { error: 'Report ID is required' },
        { status: 400 }
      );
    }

    const validShareTypes = ['public', 'private', 'password', 'expiring'];
    if (!validShareTypes.includes(shareType)) {
      return NextResponse.json(
        { error: `Invalid share type. Valid types: ${validShareTypes.join(', ')}` },
        { status: 400 }
      );
    }

    if (shareType === 'password' && !password) {
      return NextResponse.json(
        { error: 'Password is required for password-protected shares' },
        { status: 400 }
      );
    }

    if (shareType === 'expiring' && !expiresAt) {
      return NextResponse.json(
        { error: 'Expiration date is required for expiring shares' },
        { status: 400 }
      );
    }

    const shareSettings = {
      shareType,
      permissions,
      recipients: recipients.map((recipient: any) => ({
        email: recipient.email,
        name: recipient.name,
        permissions: recipient.permissions || permissions,
        notified: false
      })),
      expiresAt: expiresAt ? new Date(expiresAt) : undefined,
      password,
      allowDownload,
      allowComments,
      trackViews,
      createdBy: session.user?.email || 'anonymous'
    };

    const shareLink = ReportSharingService.createShareLink(
      reportId,
      shareSettings,
      session.user?.email || 'anonymous'
    );

    return NextResponse.json({
      success: true,
      data: {
        shareId: shareLink.id,
        shareUrl: shareLink.url,
        settings: shareLink.settings,
        analytics: shareLink.analytics
      },
    });

  } catch (error) {
    console.error('Report sharing error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const reportId = searchParams.get('reportId');
    const shareId = searchParams.get('shareId');

    if (shareId) {
      // Get specific share link
      const shareLink = ReportSharingService.getShareLink(shareId);
      
      if (!shareLink) {
        return NextResponse.json(
          { error: 'Share link not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: shareLink,
      });
    }

    if (reportId) {
      // Get all share links for a report
      const shareLinks = ReportSharingService.getReportShareLinks(reportId);
      
      return NextResponse.json({
        success: true,
        data: shareLinks,
      });
    }

    return NextResponse.json(
      { error: 'Either reportId or shareId is required' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Get share links error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { shareId, updates } = body;

    if (!shareId) {
      return NextResponse.json(
        { error: 'Share ID is required' },
        { status: 400 }
      );
    }

    const updatedShareLink = ReportSharingService.updateShareSettings(shareId, updates);
    
    if (!updatedShareLink) {
      return NextResponse.json(
        { error: 'Share link not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: updatedShareLink,
    });

  } catch (error) {
    console.error('Update share link error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const shareId = searchParams.get('shareId');

    if (!shareId) {
      return NextResponse.json(
        { error: 'Share ID is required' },
        { status: 400 }
      );
    }

    const revoked = ReportSharingService.revokeShareLink(shareId);
    
    if (!revoked) {
      return NextResponse.json(
        { error: 'Share link not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Share link revoked successfully',
    });

  } catch (error) {
    console.error('Revoke share link error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
