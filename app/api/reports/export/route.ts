import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { ReportExportService } from '@/lib/reports/report-export';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { 
      reportId, 
      format, 
      includeCharts = true, 
      includeImages = true,
      password,
      watermark 
    } = body;

    if (!reportId || !format) {
      return NextResponse.json(
        { error: 'Report ID and format are required' },
        { status: 400 }
      );
    }

    const supportedFormats = ['pdf', 'excel', 'csv', 'docx'];
    if (!supportedFormats.includes(format)) {
      return NextResponse.json(
        { error: `Unsupported format. Supported formats: ${supportedFormats.join(', ')}` },
        { status: 400 }
      );
    }

    // Mock report data for demo
    const mockReport = {
      id: reportId,
      propertyId: 'demo-property-1',
      type: 'detailed-analysis' as const,
      title: 'Property Analysis Report - 123 Demo Street',
      generatedAt: new Date(),
      generatedBy: session.user?.email || 'anonymous',
      sections: [
        {
          title: 'Executive Summary',
          content: 'This property shows strong development potential with a score of 85/100. Key strengths include excellent transit accessibility and favorable zoning.',
          charts: [
            {
              type: 'bar' as const,
              title: 'Evaluation Criteria Scores',
              data: [85, 92, 78, 88, 76],
              labels: ['Overall', 'Transit', 'Zoning', 'Size', 'Location']
            }
          ]
        },
        {
          title: 'Property Details',
          content: 'Located at 123 Demo Street, this 22,500 sq ft property offers significant development opportunities.',
          tables: [
            {
              title: 'Property Information',
              headers: ['Attribute', 'Value'],
              rows: [
                ['Address', '123 Demo Street, Los Angeles, CA'],
                ['Lot Size', '22,500 sq ft'],
                ['Zoning', 'R3'],
                ['Assessed Value', '$850,000']
              ]
            }
          ]
        },
        {
          title: 'Financial Analysis',
          content: 'The property demonstrates strong financial viability with projected ROI of 18-22%.',
          charts: [
            {
              type: 'line' as const,
              title: 'Projected Cash Flow',
              data: [100000, 120000, 145000, 175000, 210000],
              labels: ['Year 1', 'Year 2', 'Year 3', 'Year 4', 'Year 5']
            }
          ]
        }
      ],
      metadata: {
        version: '1.0',
        template: 'detailed-analysis',
        dataSourcesUsed: ['LA County Assessor', 'ZIMAS', 'LA Metro'],
        confidenceLevel: 92
      }
    };

    const exportOptions = {
      format: format as 'pdf' | 'excel' | 'csv' | 'docx',
      includeCharts,
      includeImages,
      password,
      watermark
    };

    const exportResult = await ReportExportService.exportReport(mockReport, exportOptions);

    if (!exportResult.success) {
      return NextResponse.json(
        { error: exportResult.error || 'Export failed' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        downloadUrl: exportResult.downloadUrl,
        fileName: exportResult.fileName,
        fileSize: exportResult.fileSize,
        format: format
      },
    });

  } catch (error) {
    console.error('Report export error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const reportType = searchParams.get('reportType');

    // Get supported export formats for report type
    const supportedFormats = ReportExportService.getSupportedFormats(reportType || 'detailed-analysis');

    return NextResponse.json({
      success: true,
      data: {
        supportedFormats,
        formatDescriptions: {
          pdf: 'Portable Document Format - Best for sharing and printing',
          excel: 'Microsoft Excel format - Best for data analysis',
          csv: 'Comma Separated Values - Best for data import/export',
          docx: 'Microsoft Word format - Best for editing and collaboration'
        }
      },
    });

  } catch (error) {
    console.error('Get export formats error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
