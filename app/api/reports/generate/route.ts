import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { ReportGenerator } from '@/lib/reports/report-generator';
// import { connectToDatabase, PropertyModel, PropertyReportModel } from '@/lib/db/models';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { 
      propertyId, 
      reportType = 'detailed-analysis',
      includeFinancials = true,
      customSections = []
    } = body;

    if (!propertyId) {
      return NextResponse.json(
        { error: 'Property ID is required' },
        { status: 400 }
      );
    }

    // For demo, create a mock property with evaluation results
    const property = {
      id: propertyId,
      address: '123 Demo Street, Los Angeles, CA 90210',
      parcelId: 'APN-*********',
      coordinates: { lat: 34.0522, lng: -118.2437 },
      lotSize: 22500,
      dimensions: { width: 150, length: 150 },
      zoning: 'R3',
      assessedValue: 850000,
      taxAmount: 10200,
      owner: {
        name: 'Demo Owner',
        address: '456 Owner St, Los Angeles, CA 90210',
      },
      createdAt: new Date(),
      updatedAt: new Date(),
      evaluationResults: {
        overallScore: 85,
        passed: true,
        criteria: {
          qctDdaStatus: true,
          neighborhoodChangeZone: true,
          lotSizeAnalysis: {
            size: 22500,
            isIdeal: true,
            isAcceptable: true,
            subdivisionPotential: false,
            minimumWidth: 150,
            meetsWidthRequirement: true,
          },
          densityPotential: {
            baseUnits: 180,
            bonusUnits: 90,
            totalUnits: 270,
            meets250Requirement: true,
          },
          heightRestrictions: {
            baseHeight: 65,
            bonusHeight: 30,
            totalHeight: 95,
            meets65FtRequirement: true,
          },
          siteLayout: {
            frontages: 3,
            meetsMinimumFrontages: true,
            hasPreferredFrontages: true,
          },
          topography: {
            gradeChange: 5,
            meetsRequirement: true,
          },
          historicalStatus: {
            hasDesignation: false,
            inHistoricalDistrict: false,
            isEligible: true,
          },
          environmentalConcerns: {
            hasIssues: false,
            requiresRemediation: false,
            isEligible: true,
          },
          transitAccess: {
            nearestStopDistance: 0.2,
            withinQuarterMile: true,
            peakFrequency: 12,
            offPeakFrequency: 20,
            meetsPeakRequirement: true,
            meetsOffPeakRequirement: true,
            isEligible: true,
          },
        },
        recommendations: [
          'Consider applying for additional density bonuses',
          'Explore affordable housing incentives',
        ],
        alternativeSuggestions: [],
        confidenceScore: 92,
        evaluatedAt: new Date(),
      },
    };

    // Generate the report
    const reportGenerator = new ReportGenerator();
    const report = await reportGenerator.generatePropertyReport(
      property,
      property.evaluationResults,
      reportType,
      session.user.id
    );

    // For demo, just return the report without saving
    const savedReport = { ...report, _id: `report_${Date.now()}` };

    return NextResponse.json({
      success: true,
      data: {
        reportId: savedReport._id,
        report: savedReport,
      },
    });

  } catch (error) {
    console.error('Report generation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const reportId = searchParams.get('reportId');
    const propertyId = searchParams.get('propertyId');

    // For demo, return mock reports
    const mockReports = [
      {
        id: 'report_1',
        propertyId: 'prop_1',
        type: 'detailed-analysis',
        title: 'Detailed Property Analysis - 123 Demo Street',
        generatedAt: new Date().toISOString(),
        generatedBy: session.user.id,
        metadata: {
          version: '1.0',
          template: 'detailed-analysis',
          dataSourcesUsed: ['County Assessor', 'Zoning Department', 'Transit Authority'],
          confidenceLevel: 92,
        },
      },
      {
        id: 'report_2',
        propertyId: 'prop_2',
        type: 'executive-summary',
        title: 'Executive Summary - 456 Oak Avenue',
        generatedAt: new Date(Date.now() - 86400000).toISOString(),
        generatedBy: session.user.id,
        metadata: {
          version: '1.0',
          template: 'executive-summary',
          dataSourcesUsed: ['County Assessor', 'Zoning Department'],
          confidenceLevel: 88,
        },
      },
    ];

    if (reportId) {
      // Get specific report
      const report = mockReports.find(r => r.id === reportId);

      if (!report) {
        return NextResponse.json(
          { error: 'Report not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: report,
      });
    } else if (propertyId) {
      // Get all reports for a property
      const reports = mockReports.filter(r => r.propertyId === propertyId);

      return NextResponse.json({
        success: true,
        data: reports,
      });
    } else {
      // Get user's recent reports
      return NextResponse.json({
        success: true,
        data: mockReports,
      });
    }

  } catch (error) {
    console.error('Get reports error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
