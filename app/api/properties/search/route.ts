import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
// import { GoogleMapsClient } from '@/lib/mapping/google-maps-client';
// import { CountyAssessorScraper } from '@/lib/scraping/county-assessor-scraper';
import { PropertyEvaluationEngine } from '@/lib/evaluation/criteria-engine';
// import { connectToDatabase, PropertyModel } from '@/lib/db/models';
import { Property } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { 
      location, 
      radius = 5, 
      filters = {} 
    } = body;

    if (!location) {
      return NextResponse.json(
        { error: 'Location is required' },
        { status: 400 }
      );
    }

    // Mock geocoding for demo
    const searchCenter = { lat: 34.0522, lng: -118.2437 }; // Los Angeles
    const geocodeResult = {
      address: location,
      coordinates: searchCenter,
      formattedAddress: location,
      addressComponents: [],
      placeId: 'demo-place-id',
    };

    // Generate mock properties for demo
    const properties = generateMockProperties(searchCenter, radius, filters);

    // Evaluate each property
    const evaluatedProperties = await Promise.all(
      properties.map(async (property) => {
        try {
          const evaluation = await PropertyEvaluationEngine.evaluateProperty(property);
          return {
            ...property,
            evaluationResults: evaluation,
          };
        } catch (error) {
          console.error(`Failed to evaluate property ${property.id}:`, error);
          return property;
        }
      })
    );

    // Filter based on evaluation criteria if specified
    let filteredProperties = evaluatedProperties;
    if (filters.requiresQctDda) {
      filteredProperties = filteredProperties.filter(
        p => p.evaluationResults?.criteria.qctDdaStatus
      );
    }

    if (filters.minScore) {
      filteredProperties = filteredProperties.filter(
        p => (p.evaluationResults?.overallScore || 0) >= filters.minScore
      );
    }

    // Sort by evaluation score (highest first)
    filteredProperties.sort((a, b) => {
      const scoreA = a.evaluationResults?.overallScore || 0;
      const scoreB = b.evaluationResults?.overallScore || 0;
      return scoreB - scoreA;
    });

    // In demo mode, we skip saving to database
    console.log(`Demo: Found ${filteredProperties.length} properties for ${location}`);

    return NextResponse.json({
      success: true,
      data: {
        searchLocation: geocodeResult,
        properties: filteredProperties,
        totalFound: filteredProperties.length,
        searchRadius: radius,
        filters: filters,
      },
    });

  } catch (error) {
    console.error('Property search error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Removed searchPropertiesInArea function - using generateMockProperties directly

function generateMockProperties(
  center: { lat: number; lng: number },
  radiusMiles: number,
  filters: any
): Property[] {
  const properties: Property[] = [];
  const numProperties = Math.min(20, Math.max(5, Math.floor(Math.random() * 15) + 5));

  for (let i = 0; i < numProperties; i++) {
    // Generate random coordinates within radius
    const angle = Math.random() * 2 * Math.PI;
    const distance = Math.random() * radiusMiles;
    
    // Convert miles to degrees (approximate)
    const latOffset = (distance * Math.cos(angle)) / 69;
    const lngOffset = (distance * Math.sin(angle)) / (69 * Math.cos(center.lat * Math.PI / 180));

    const coordinates = {
      lat: center.lat + latOffset,
      lng: center.lng + lngOffset,
    };

    // Generate property data
    const lotSize = Math.floor(Math.random() * 30000) + 10000; // 10k-40k sq ft
    const width = Math.floor(Math.sqrt(lotSize) * (0.8 + Math.random() * 0.4));
    const length = Math.floor(lotSize / width);

    const zoningCodes = ['R1', 'R2', 'R3', 'R4', 'R5'];
    const zoning = zoningCodes[Math.floor(Math.random() * zoningCodes.length)];

    const assessedValue = Math.floor((Math.random() * 1500000) + 500000); // $500k-$2M
    const taxRate = 0.012; // 1.2%
    const taxAmount = Math.floor(assessedValue * taxRate);

    const streetNumbers = [
      '123', '456', '789', '1011', '1213', '1415', '1617', '1819', '2021', '2223'
    ];
    const streetNames = [
      'Main St', 'Oak Ave', 'Pine St', 'Elm Dr', 'Maple Way', 'Cedar Blvd',
      'Willow Ln', 'Birch St', 'Ash Ave', 'Poplar Dr'
    ];

    const streetNumber = streetNumbers[Math.floor(Math.random() * streetNumbers.length)];
    const streetName = streetNames[Math.floor(Math.random() * streetNames.length)];
    const address = `${streetNumber} ${streetName}, Los Angeles, CA 90210`;

    const ownerNames = [
      'John Smith', 'Jane Doe', 'Robert Johnson', 'Mary Williams', 'David Brown',
      'Sarah Davis', 'Michael Miller', 'Lisa Wilson', 'James Moore', 'Jennifer Taylor'
    ];

    const property: Property = {
      id: `prop_${Date.now()}_${i}`,
      address,
      parcelId: `APN-${Math.floor(Math.random() * *********) + *********}`,
      coordinates,
      lotSize,
      dimensions: { width, length },
      zoning,
      assessedValue,
      taxAmount,
      owner: {
        name: ownerNames[Math.floor(Math.random() * ownerNames.length)],
        address: `${Math.floor(Math.random() * 9999) + 1} Owner St, Los Angeles, CA 90210`,
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Apply filters
    if (filters.minLotSize && lotSize < filters.minLotSize) continue;
    if (filters.maxLotSize && lotSize > filters.maxLotSize) continue;
    if (filters.minPrice && assessedValue < filters.minPrice) continue;
    if (filters.maxPrice && assessedValue > filters.maxPrice) continue;
    if (filters.zoning && filters.zoning.length > 0 && !filters.zoning.includes(zoning)) continue;

    properties.push(property);
  }

  return properties;
}
