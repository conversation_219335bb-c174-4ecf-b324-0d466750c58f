'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Users, 
  Database, 
  Activity, 
  Settings,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  Server,
  Workflow,
  Bell,
  BarChart3
} from 'lucide-react';

export default function AdminDashboard() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');
  const [systemMetrics, setSystemMetrics] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (status === 'loading') return;
    if (!session) {
      router.push('/auth/signin');
      return;
    }

    // Check if user is admin
    if (session.user?.role !== 'admin') {
      router.push('/dashboard');
      return;
    }

    loadSystemMetrics();
  }, [session, status, router]);

  const loadSystemMetrics = async () => {
    try {
      // Mock system metrics - in production, fetch from APIs
      const metrics = {
        users: {
          total: 1247,
          active: 342,
          newToday: 23,
          growth: 12.5,
        },
        properties: {
          total: 15678,
          evaluated: 8934,
          qualified: 2156,
          newToday: 89,
        },
        reports: {
          total: 3421,
          generatedToday: 45,
          averageTime: 2.3,
          successRate: 98.7,
        },
        system: {
          uptime: 99.9,
          responseTime: 245,
          errorRate: 0.02,
          dataQuality: 94.2,
        },
        workflows: {
          active: 12,
          executions: 1567,
          successRate: 96.8,
          avgDuration: 4.2,
        },
        dataSources: {
          active: 8,
          syncing: 2,
          errors: 1,
          lastSync: new Date(),
        },
      };
      
      setSystemMetrics(metrics);
    } catch (error) {
      console.error('Failed to load system metrics:', error);
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: BarChart3 },
    { id: 'users', label: 'Users', icon: Users },
    { id: 'data', label: 'Data Sources', icon: Database },
    { id: 'workflows', label: 'Workflows', icon: Workflow },
    { id: 'system', label: 'System Health', icon: Server },
    { id: 'notifications', label: 'Notifications', icon: Bell },
  ];

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!session || session.user?.role !== 'admin') return null;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Admin Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            System administration and monitoring
          </p>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar */}
          <div className="lg:w-64">
            <nav className="space-y-1">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                      activeTab === tab.id
                        ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50 dark:text-gray-400 dark:hover:text-white dark:hover:bg-gray-800'
                    }`}
                  >
                    <Icon className="w-5 h-5 mr-3" />
                    {tab.label}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Content */}
          <div className="flex-1">
            {activeTab === 'overview' && (
              <div className="space-y-6">
                {/* Key Metrics */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            Total Users
                          </p>
                          <p className="text-2xl font-bold text-gray-900 dark:text-white">
                            {systemMetrics?.users.total.toLocaleString()}
                          </p>
                        </div>
                        <Users className="w-8 h-8 text-blue-600" />
                      </div>
                      <div className="mt-2 flex items-center text-sm">
                        <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                        <span className="text-green-600">+{systemMetrics?.users.growth}%</span>
                        <span className="text-gray-500 ml-1">this month</span>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            Properties
                          </p>
                          <p className="text-2xl font-bold text-gray-900 dark:text-white">
                            {systemMetrics?.properties.total.toLocaleString()}
                          </p>
                        </div>
                        <Database className="w-8 h-8 text-green-600" />
                      </div>
                      <div className="mt-2 text-sm text-gray-500">
                        {systemMetrics?.properties.qualified.toLocaleString()} qualified
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            Reports Generated
                          </p>
                          <p className="text-2xl font-bold text-gray-900 dark:text-white">
                            {systemMetrics?.reports.total.toLocaleString()}
                          </p>
                        </div>
                        <Activity className="w-8 h-8 text-purple-600" />
                      </div>
                      <div className="mt-2 text-sm text-gray-500">
                        {systemMetrics?.reports.generatedToday} today
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            System Uptime
                          </p>
                          <p className="text-2xl font-bold text-gray-900 dark:text-white">
                            {systemMetrics?.system.uptime}%
                          </p>
                        </div>
                        <CheckCircle className="w-8 h-8 text-green-500" />
                      </div>
                      <div className="mt-2 text-sm text-gray-500">
                        {systemMetrics?.system.responseTime}ms avg response
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* System Status */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>System Health</CardTitle>
                      <CardDescription>Current system status and performance</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">API Response Time</span>
                          <span className="text-sm text-gray-600">{systemMetrics?.system.responseTime}ms</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">Error Rate</span>
                          <span className="text-sm text-gray-600">{(systemMetrics?.system.errorRate * 100).toFixed(2)}%</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">Data Quality Score</span>
                          <span className="text-sm text-gray-600">{systemMetrics?.system.dataQuality}%</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Recent Activity</CardTitle>
                      <CardDescription>Latest system events and alerts</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-start space-x-3">
                          <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                          <div className="flex-1">
                            <p className="text-sm font-medium">Data sync completed</p>
                            <p className="text-xs text-gray-500">2 minutes ago</p>
                          </div>
                        </div>
                        <div className="flex items-start space-x-3">
                          <AlertTriangle className="w-4 h-4 text-yellow-500 mt-0.5" />
                          <div className="flex-1">
                            <p className="text-sm font-medium">High API usage detected</p>
                            <p className="text-xs text-gray-500">15 minutes ago</p>
                          </div>
                        </div>
                        <div className="flex items-start space-x-3">
                          <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                          <div className="flex-1">
                            <p className="text-sm font-medium">Weekly report generated</p>
                            <p className="text-xs text-gray-500">1 hour ago</p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            )}

            {activeTab === 'users' && (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>User Management</CardTitle>
                    <CardDescription>Manage user accounts and permissions</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">Active Users</h4>
                          <p className="text-sm text-gray-600">{systemMetrics?.users.active} users online</p>
                        </div>
                        <Button variant="outline">View All Users</Button>
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">New Registrations</h4>
                          <p className="text-sm text-gray-600">{systemMetrics?.users.newToday} new users today</p>
                        </div>
                        <Button variant="outline">Registration Settings</Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {activeTab === 'data' && (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Data Sources</CardTitle>
                    <CardDescription>Monitor and manage external data integrations</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">Active Sources</h4>
                          <p className="text-sm text-gray-600">{systemMetrics?.dataSources.active} sources connected</p>
                        </div>
                        <Button variant="outline">Manage Sources</Button>
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">Sync Status</h4>
                          <p className="text-sm text-gray-600">
                            {systemMetrics?.dataSources.syncing} syncing, {systemMetrics?.dataSources.errors} errors
                          </p>
                        </div>
                        <Button variant="outline">Force Sync</Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {activeTab === 'workflows' && (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Workflow Automation</CardTitle>
                    <CardDescription>Monitor and manage automated workflows</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">Active Workflows</h4>
                          <p className="text-sm text-gray-600">{systemMetrics?.workflows.active} workflows running</p>
                        </div>
                        <Button variant="outline">Manage Workflows</Button>
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">Success Rate</h4>
                          <p className="text-sm text-gray-600">{systemMetrics?.workflows.successRate}% successful executions</p>
                        </div>
                        <Button variant="outline">View Logs</Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {activeTab === 'system' && (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>System Health</CardTitle>
                    <CardDescription>Monitor system performance and health metrics</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h4 className="font-medium mb-3">Performance Metrics</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm">CPU Usage</span>
                            <span className="text-sm">45%</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm">Memory Usage</span>
                            <span className="text-sm">62%</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm">Disk Usage</span>
                            <span className="text-sm">38%</span>
                          </div>
                        </div>
                      </div>
                      <div>
                        <h4 className="font-medium mb-3">Service Status</h4>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-sm">API Server</span>
                            <CheckCircle className="w-4 h-4 text-green-500" />
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm">Database</span>
                            <CheckCircle className="w-4 h-4 text-green-500" />
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm">Cache</span>
                            <CheckCircle className="w-4 h-4 text-green-500" />
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {activeTab === 'notifications' && (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Notification System</CardTitle>
                    <CardDescription>Manage system notifications and alerts</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">Email Notifications</h4>
                          <p className="text-sm text-gray-600">1,234 sent today</p>
                        </div>
                        <Button variant="outline">Configure</Button>
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">System Alerts</h4>
                          <p className="text-sm text-gray-600">3 active alerts</p>
                        </div>
                        <Button variant="outline">View Alerts</Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
