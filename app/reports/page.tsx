'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  FileText, 
  Download, 
  Share, 
  Search,
  Calendar,
  Filter,
  Plus,
  Eye,
  Trash2
} from 'lucide-react';
import toast from 'react-hot-toast';

interface Report {
  id: string;
  propertyId: string;
  type: string;
  title: string;
  generatedAt: string;
  generatedBy: string;
  metadata: {
    version: string;
    template: string;
    dataSourcesUsed: string[];
    confidenceLevel: number;
  };
}

export default function ReportsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [reports, setReports] = useState<Report[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('all');

  useEffect(() => {
    if (status === 'loading') return;
    if (!session) {
      router.push('/auth/signin');
      return;
    }

    fetchReports();
  }, [session, status, router]);

  const fetchReports = async () => {
    try {
      const response = await fetch('/api/reports/generate');
      const data = await response.json();

      if (data.success) {
        setReports(data.data);
      } else {
        toast.error('Failed to load reports');
      }
    } catch (error) {
      toast.error('Error loading reports');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteReport = async (reportId: string) => {
    if (!confirm('Are you sure you want to delete this report?')) {
      return;
    }

    try {
      const response = await fetch(`/api/reports/${reportId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setReports(reports.filter(report => report.id !== reportId));
        toast.success('Report deleted successfully');
      } else {
        toast.error('Failed to delete report');
      }
    } catch (error) {
      toast.error('Error deleting report');
    }
  };

  const filteredReports = reports.filter(report => {
    const matchesSearch = report.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         report.type.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = filterType === 'all' || report.type === filterType;
    
    return matchesSearch && matchesFilter;
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getReportTypeLabel = (type: string) => {
    const labels: { [key: string]: string } = {
      'executive-summary': 'Executive Summary',
      'detailed-analysis': 'Detailed Analysis',
      'feasibility-study': 'Feasibility Study',
      'comparison': 'Property Comparison',
    };
    return labels[type] || type;
  };

  const getReportTypeColor = (type: string) => {
    const colors: { [key: string]: string } = {
      'executive-summary': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      'detailed-analysis': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      'feasibility-study': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
      'comparison': 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
    };
    return colors[type] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!session) return null;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Reports
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-2">
                View and manage your property analysis reports
              </p>
            </div>
            <Link href="/properties/search">
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                New Report
              </Button>
            </Link>
          </div>
        </div>

        {/* Filters and Search */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    type="text"
                    placeholder="Search reports..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                >
                  <option value="all">All Types</option>
                  <option value="executive-summary">Executive Summary</option>
                  <option value="detailed-analysis">Detailed Analysis</option>
                  <option value="feasibility-study">Feasibility Study</option>
                  <option value="comparison">Property Comparison</option>
                </select>
                <Button variant="outline" size="sm">
                  <Filter className="w-4 h-4 mr-2" />
                  More Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Reports Grid */}
        {filteredReports.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <FileText className="w-16 h-16 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No reports found
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                {searchQuery || filterType !== 'all' 
                  ? 'Try adjusting your search or filters'
                  : 'Generate your first property report to get started'
                }
              </p>
              <Link href="/properties/search">
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Create Report
                </Button>
              </Link>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredReports.map((report) => (
              <Card key={report.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getReportTypeColor(report.type)}`}>
                          {getReportTypeLabel(report.type)}
                        </span>
                        <span className="text-xs text-gray-500">
                          v{report.metadata.version}
                        </span>
                      </div>
                      <CardTitle className="text-lg line-clamp-2">
                        {report.title}
                      </CardTitle>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteReport(report.id)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                      <Calendar className="w-4 h-4 mr-2" />
                      {formatDate(report.generatedAt)}
                    </div>
                    
                    <div className="text-sm">
                      <span className="text-gray-600 dark:text-gray-400">Confidence: </span>
                      <span className="font-medium">{report.metadata.confidenceLevel}%</span>
                    </div>

                    <div className="text-sm">
                      <span className="text-gray-600 dark:text-gray-400">Data Sources: </span>
                      <span className="font-medium">{report.metadata.dataSourcesUsed.length}</span>
                    </div>

                    <div className="flex space-x-2 pt-3">
                      <Link href={`/reports/${report.id}`} className="flex-1">
                        <Button size="sm" className="w-full">
                          <Eye className="w-4 h-4 mr-2" />
                          View
                        </Button>
                      </Link>
                      <Button variant="outline" size="sm">
                        <Download className="w-4 h-4 mr-2" />
                        Export
                      </Button>
                      <Button variant="outline" size="sm">
                        <Share className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Stats */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {reports.length}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Total Reports
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {reports.filter(r => r.type === 'detailed-analysis').length}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Detailed Analysis
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {reports.filter(r => r.type === 'feasibility-study').length}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Feasibility Studies
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {Math.round(reports.reduce((acc, r) => acc + r.metadata.confidenceLevel, 0) / reports.length) || 0}%
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Avg Confidence
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
