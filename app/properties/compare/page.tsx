'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Plus, 
  X, 
  Download, 
  Share2, 
  BarChart3,
  TrendingUp,
  TrendingDown,
  Minus,
  CheckCircle,
  AlertTriangle
} from 'lucide-react';
import toast from 'react-hot-toast';

interface Property {
  id: string;
  address: string;
  assessedValue: number;
  lotSize: number;
  zoning: string;
  coordinates: { lat: number; lng: number };
}

interface ComparisonResult {
  criteriaId: string;
  values: any[];
  rankings: number[];
  winner: number;
}

interface PropertyComparison {
  id: string;
  properties: Property[];
  results: ComparisonResult[];
  summary: {
    overallWinner: number;
    recommendations: string[];
  };
}

export default function PropertyComparePage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [selectedProperties, setSelectedProperties] = useState<Property[]>([]);
  const [comparison, setComparison] = useState<PropertyComparison | null>(null);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [availableProperties] = useState<Property[]>([
    {
      id: 'prop-1',
      address: '123 Demo Street 1, Los Angeles, CA 90210',
      assessedValue: 800000,
      lotSize: 20000,
      zoning: 'R3',
      coordinates: { lat: 34.0522, lng: -118.2437 }
    },
    {
      id: 'prop-2',
      address: '223 Demo Street 2, Los Angeles, CA 90210',
      assessedValue: 950000,
      lotSize: 22500,
      zoning: 'R4',
      coordinates: { lat: 34.0532, lng: -118.2447 }
    },
    {
      id: 'prop-3',
      address: '323 Demo Street 3, Los Angeles, CA 90210',
      assessedValue: 750000,
      lotSize: 18000,
      zoning: 'R3',
      coordinates: { lat: 34.0512, lng: -118.2427 }
    },
    {
      id: 'prop-4',
      address: '423 Demo Street 4, Los Angeles, CA 90210',
      assessedValue: 1100000,
      lotSize: 25000,
      zoning: 'R4',
      coordinates: { lat: 34.0542, lng: -118.2457 }
    },
    {
      id: 'prop-5',
      address: '523 Demo Street 5, Los Angeles, CA 90210',
      assessedValue: 850000,
      lotSize: 21000,
      zoning: 'R3',
      coordinates: { lat: 34.0502, lng: -118.2417 }
    }
  ]);

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
    }
  }, [status, router]);

  const addProperty = (property: Property) => {
    if (selectedProperties.length >= 5) {
      toast.error('Maximum 5 properties can be compared');
      return;
    }

    if (selectedProperties.find(p => p.id === property.id)) {
      toast.error('Property already selected');
      return;
    }

    setSelectedProperties([...selectedProperties, property]);
    setComparison(null); // Clear previous comparison
  };

  const removeProperty = (propertyId: string) => {
    setSelectedProperties(selectedProperties.filter(p => p.id !== propertyId));
    setComparison(null); // Clear previous comparison
  };

  const compareProperties = async () => {
    if (selectedProperties.length < 2) {
      toast.error('Select at least 2 properties to compare');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/properties/compare', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          propertyIds: selectedProperties.map(p => p.id)
        }),
      });

      const data = await response.json();

      if (data.success) {
        setComparison(data.data);
        toast.success('Properties compared successfully');
      } else {
        toast.error('Failed to compare properties');
      }
    } catch (error) {
      toast.error('Error comparing properties');
    } finally {
      setLoading(false);
    }
  };

  const exportComparison = async (format: 'pdf' | 'excel' | 'csv') => {
    if (!comparison) return;

    try {
      const response = await fetch('/api/reports/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reportId: comparison.id,
          format,
          reportType: 'comparison'
        }),
      });

      const data = await response.json();

      if (data.success) {
        // In a real app, this would trigger a download
        toast.success(`Comparison exported as ${format.toUpperCase()}`);
      } else {
        toast.error('Failed to export comparison');
      }
    } catch (error) {
      toast.error('Error exporting comparison');
    }
  };

  const shareComparison = async () => {
    if (!comparison) return;

    try {
      const response = await fetch('/api/reports/share', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reportId: comparison.id,
          shareType: 'public',
          allowDownload: true,
          allowComments: true
        }),
      });

      const data = await response.json();

      if (data.success) {
        navigator.clipboard.writeText(data.data.shareUrl);
        toast.success('Share link copied to clipboard');
      } else {
        toast.error('Failed to create share link');
      }
    } catch (error) {
      toast.error('Error creating share link');
    }
  };

  const filteredProperties = availableProperties.filter(property =>
    property.address.toLowerCase().includes(searchQuery.toLowerCase()) &&
    !selectedProperties.find(p => p.id === property.id)
  );

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getRankingIcon = (ranking: number, total: number) => {
    if (ranking === 1) return <CheckCircle className="w-4 h-4 text-green-500" />;
    if (ranking === total) return <AlertTriangle className="w-4 h-4 text-red-500" />;
    return <Minus className="w-4 h-4 text-yellow-500" />;
  };

  if (status === 'loading') {
    return <div className="flex justify-center items-center min-h-screen">Loading...</div>;
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Property Comparison
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Compare up to 5 properties side by side to make informed investment decisions
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Property Selection */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle>Select Properties</CardTitle>
                <CardDescription>
                  Choose properties to compare ({selectedProperties.length}/5 selected)
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Search */}
                  <input
                    type="text"
                    placeholder="Search properties..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />

                  {/* Selected Properties */}
                  {selectedProperties.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm text-gray-700 dark:text-gray-300">
                        Selected Properties
                      </h4>
                      {selectedProperties.map((property) => (
                        <div
                          key={property.id}
                          className="flex items-center justify-between p-2 bg-blue-50 dark:bg-blue-900/20 rounded-md"
                        >
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                              {property.address}
                            </p>
                            <p className="text-xs text-gray-500">
                              {formatCurrency(property.assessedValue)} • {property.lotSize.toLocaleString()} sq ft
                            </p>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeProperty(property.id)}
                          >
                            <X className="w-4 h-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Available Properties */}
                  <div className="space-y-2">
                    <h4 className="font-medium text-sm text-gray-700 dark:text-gray-300">
                      Available Properties
                    </h4>
                    <div className="max-h-64 overflow-y-auto space-y-2">
                      {filteredProperties.map((property) => (
                        <div
                          key={property.id}
                          className="flex items-center justify-between p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800"
                        >
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                              {property.address}
                            </p>
                            <p className="text-xs text-gray-500">
                              {formatCurrency(property.assessedValue)} • {property.lotSize.toLocaleString()} sq ft
                            </p>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => addProperty(property)}
                            disabled={selectedProperties.length >= 5}
                          >
                            <Plus className="w-4 h-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Compare Button */}
                  <Button
                    onClick={compareProperties}
                    disabled={selectedProperties.length < 2 || loading}
                    className="w-full"
                  >
                    {loading ? 'Comparing...' : 'Compare Properties'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Comparison Results */}
          <div className="lg:col-span-2">
            {comparison ? (
              <div className="space-y-6">
                {/* Comparison Header */}
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle>Comparison Results</CardTitle>
                        <CardDescription>
                          Analysis of {comparison.properties.length} properties
                        </CardDescription>
                      </div>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm" onClick={() => exportComparison('pdf')}>
                          <Download className="w-4 h-4 mr-2" />
                          Export PDF
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => exportComparison('excel')}>
                          <Download className="w-4 h-4 mr-2" />
                          Export Excel
                        </Button>
                        <Button variant="outline" size="sm" onClick={shareComparison}>
                          <Share2 className="w-4 h-4 mr-2" />
                          Share
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {/* Winner */}
                    <div className="mb-4 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <div className="flex items-center">
                        <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
                        <span className="font-medium text-green-800 dark:text-green-200">
                          Overall Winner: {comparison.properties[comparison.summary.overallWinner]?.address}
                        </span>
                      </div>
                    </div>

                    {/* Recommendations */}
                    <div className="space-y-2">
                      <h4 className="font-medium text-gray-900 dark:text-white">Recommendations</h4>
                      <ul className="space-y-1">
                        {comparison.summary.recommendations.map((rec, index) => (
                          <li key={index} className="text-sm text-gray-600 dark:text-gray-400 flex items-start">
                            <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                            {rec}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </CardContent>
                </Card>

                {/* Detailed Comparison Table */}
                <Card>
                  <CardHeader>
                    <CardTitle>Detailed Comparison</CardTitle>
                    <CardDescription>
                      Side-by-side analysis of all criteria
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="overflow-x-auto">
                      <table className="w-full text-sm">
                        <thead>
                          <tr className="border-b">
                            <th className="text-left py-2 px-3 font-medium">Criteria</th>
                            {comparison.properties.map((property, index) => (
                              <th key={property.id} className="text-center py-2 px-3 font-medium">
                                <div className="truncate max-w-32" title={property.address}>
                                  Property {index + 1}
                                </div>
                              </th>
                            ))}
                          </tr>
                        </thead>
                        <tbody>
                          {/* Mock comparison criteria */}
                          <tr className="border-b">
                            <td className="py-2 px-3 font-medium">Assessed Value</td>
                            {comparison.properties.map((property, index) => (
                              <td key={property.id} className="text-center py-2 px-3">
                                <div className="flex items-center justify-center">
                                  {getRankingIcon(index + 1, comparison.properties.length)}
                                  <span className="ml-2">{formatCurrency(property.assessedValue)}</span>
                                </div>
                              </td>
                            ))}
                          </tr>
                          <tr className="border-b">
                            <td className="py-2 px-3 font-medium">Lot Size</td>
                            {comparison.properties.map((property, index) => (
                              <td key={property.id} className="text-center py-2 px-3">
                                <div className="flex items-center justify-center">
                                  {getRankingIcon(index + 1, comparison.properties.length)}
                                  <span className="ml-2">{property.lotSize.toLocaleString()} sq ft</span>
                                </div>
                              </td>
                            ))}
                          </tr>
                          <tr className="border-b">
                            <td className="py-2 px-3 font-medium">Zoning</td>
                            {comparison.properties.map((property) => (
                              <td key={property.id} className="text-center py-2 px-3">
                                <Badge variant="outline">{property.zoning}</Badge>
                              </td>
                            ))}
                          </tr>
                          <tr className="border-b">
                            <td className="py-2 px-3 font-medium">Price per Sq Ft</td>
                            {comparison.properties.map((property, index) => (
                              <td key={property.id} className="text-center py-2 px-3">
                                <div className="flex items-center justify-center">
                                  {getRankingIcon(index + 1, comparison.properties.length)}
                                  <span className="ml-2">
                                    ${Math.round(property.assessedValue / property.lotSize)}
                                  </span>
                                </div>
                              </td>
                            ))}
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <BarChart3 className="w-12 h-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    No Comparison Yet
                  </h3>
                  <p className="text-gray-500 text-center">
                    Select at least 2 properties from the left panel and click "Compare Properties" to see detailed analysis.
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
