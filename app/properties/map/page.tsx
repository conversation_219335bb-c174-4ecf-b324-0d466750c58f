'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { PropertyMap, useGoogleMaps } from '@/components/maps/property-map';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Search, 
  Filter,
  MapPin,
  List,
  Loader2
} from 'lucide-react';
import { Property } from '@/types';
import toast from 'react-hot-toast';

export default function PropertyMapPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const isGoogleMapsLoaded = useGoogleMaps();
  
  const [properties, setProperties] = useState<Property[]>([]);
  const [selectedProperty, setSelectedProperty] = useState<Property | null>(null);
  const [searchLocation, setSearchLocation] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [showSidebar, setShowSidebar] = useState(true);
  const [showTransitStops, setShowTransitStops] = useState(false);
  const [showZoning, setShowZoning] = useState(false);
  const [mapCenter, setMapCenter] = useState<{ lat: number; lng: number }>({
    lat: 34.0522,
    lng: -118.2437, // Los Angeles default
  });

  useEffect(() => {
    if (status === 'loading') return;
    if (!session) {
      router.push('/auth/signin');
      return;
    }
  }, [session, status, router]);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchLocation.trim()) {
      toast.error('Please enter a search location');
      return;
    }

    setIsSearching(true);
    try {
      const response = await fetch('/api/properties/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          location: searchLocation,
          radius: 5,
          filters: {},
        }),
      });

      const data = await response.json();

      if (data.success) {
        setProperties(data.data.properties);
        setMapCenter(data.data.searchLocation.coordinates);
        toast.success(`Found ${data.data.properties.length} properties`);
      } else {
        toast.error('Search failed. Please try again.');
      }
    } catch (error) {
      toast.error('Search failed. Please try again.');
    } finally {
      setIsSearching(false);
    }
  };

  const handlePropertySelect = (property: Property) => {
    setSelectedProperty(property);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  if (status === 'loading' || !isGoogleMapsLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading map...</p>
        </div>
      </div>
    );
  }

  if (!session) return null;

  return (
    <div className="h-screen flex flex-col bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b px-4 py-3">
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-bold text-gray-900 dark:text-white">
            Property Map
          </h1>
          
          {/* Search */}
          <form onSubmit={handleSearch} className="flex items-center space-x-2 flex-1 max-w-md mx-4">
            <div className="relative flex-1">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                type="text"
                placeholder="Search location..."
                value={searchLocation}
                onChange={(e) => setSearchLocation(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button type="submit" disabled={isSearching}>
              {isSearching ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Search className="w-4 h-4" />
              )}
            </Button>
          </form>

          {/* Controls */}
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowSidebar(!showSidebar)}
            >
              <List className="w-4 h-4 mr-2" />
              {showSidebar ? 'Hide' : 'Show'} List
            </Button>
            <Button variant="outline" size="sm">
              <Filter className="w-4 h-4 mr-2" />
              Filters
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Sidebar */}
        {showSidebar && (
          <div className="w-80 bg-white dark:bg-gray-800 border-r overflow-y-auto">
            <div className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Properties ({properties.length})
                </h2>
              </div>

              {/* Map Controls */}
              <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  Map Layers
                </h3>
                <div className="space-y-2">
                  <label className="flex items-center space-x-2 text-sm">
                    <input
                      type="checkbox"
                      checked={showTransitStops}
                      onChange={(e) => setShowTransitStops(e.target.checked)}
                      className="rounded"
                    />
                    <span>Transit Stops</span>
                  </label>
                  <label className="flex items-center space-x-2 text-sm">
                    <input
                      type="checkbox"
                      checked={showZoning}
                      onChange={(e) => setShowZoning(e.target.checked)}
                      className="rounded"
                    />
                    <span>Zoning Boundaries</span>
                  </label>
                </div>
              </div>

              {/* Property List */}
              <div className="space-y-3">
                {properties.length === 0 ? (
                  <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                    <MapPin className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>No properties found</p>
                    <p className="text-sm">Search for a location to see properties</p>
                  </div>
                ) : (
                  properties.map((property) => (
                    <Card 
                      key={property.id}
                      className={`cursor-pointer transition-colors ${
                        selectedProperty?.id === property.id 
                          ? 'ring-2 ring-blue-500' 
                          : 'hover:bg-gray-50 dark:hover:bg-gray-700'
                      }`}
                      onClick={() => handlePropertySelect(property)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between mb-2">
                          <h3 className="font-medium text-gray-900 dark:text-white text-sm">
                            {property.address}
                          </h3>
                          {property.evaluationResults && (
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              property.evaluationResults.passed
                                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                            }`}>
                              {property.evaluationResults.passed ? 'Qualified' : 'Not Qualified'}
                            </span>
                          )}
                        </div>
                        
                        <div className="space-y-1 text-xs text-gray-600 dark:text-gray-400">
                          <div className="flex justify-between">
                            <span>Lot Size:</span>
                            <span>{formatNumber(property.lotSize)} sq ft</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Zoning:</span>
                            <span>{property.zoning}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Value:</span>
                            <span>{formatCurrency(property.assessedValue)}</span>
                          </div>
                          {property.evaluationResults && (
                            <div className="flex justify-between">
                              <span>Score:</span>
                              <span className="font-medium">
                                {property.evaluationResults.overallScore}/100
                              </span>
                            </div>
                          )}
                        </div>

                        <Button 
                          size="sm" 
                          className="w-full mt-3"
                          onClick={(e) => {
                            e.stopPropagation();
                            router.push(`/properties/${property.id}`);
                          }}
                        >
                          View Details
                        </Button>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </div>
          </div>
        )}

        {/* Map */}
        <div className="flex-1 relative">
          <PropertyMap
            properties={properties}
            center={mapCenter}
            height="100%"
            onPropertySelect={handlePropertySelect}
            selectedProperty={selectedProperty || undefined}
            showTransitStops={showTransitStops}
            showZoning={showZoning}
          />
        </div>
      </div>
    </div>
  );
}
