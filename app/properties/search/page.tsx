'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Search, 
  MapPin, 
  Filter,
  Loader2,
  Building,
  DollarSign,
  Ruler,
  Calendar
} from 'lucide-react';
import toast from 'react-hot-toast';

export default function PropertySearchPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [filters, setFilters] = useState({
    minLotSize: '',
    maxLotSize: '',
    minPrice: '',
    maxPrice: '',
    zoning: '',
    requiresQctDda: false,
  });

  useEffect(() => {
    if (status === 'loading') return;
    if (!session) router.push('/auth/signin');
  }, [session, status, router]);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchQuery.trim()) {
      toast.error('Please enter a search location');
      return;
    }

    setIsSearching(true);
    try {
      // Simulate API call - replace with actual implementation
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock search results
      const mockResults = [
        {
          id: '1',
          address: '123 Main Street, Los Angeles, CA 90210',
          parcelId: 'APN-123456789',
          lotSize: 22500,
          assessedValue: 850000,
          zoning: 'R3',
          coordinates: { lat: 34.0522, lng: -118.2437 },
          evaluationScore: 85,
          passed: true,
        },
        {
          id: '2',
          address: '456 Oak Avenue, Los Angeles, CA 90211',
          parcelId: 'APN-987654321',
          lotSize: 18750,
          assessedValue: 720000,
          zoning: 'R2',
          coordinates: { lat: 34.0622, lng: -118.2537 },
          evaluationScore: 72,
          passed: true,
        },
        {
          id: '3',
          address: '789 Pine Street, Los Angeles, CA 90212',
          parcelId: 'APN-456789123',
          lotSize: 15000,
          assessedValue: 650000,
          zoning: 'R1',
          coordinates: { lat: 34.0722, lng: -118.2637 },
          evaluationScore: 45,
          passed: false,
        },
      ];

      setSearchResults(mockResults);
      toast.success(`Found ${mockResults.length} properties`);
    } catch (error) {
      toast.error('Search failed. Please try again.');
    } finally {
      setIsSearching(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!session) return null;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Property Search
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Find properties that match your acquisition criteria
          </p>
        </div>

        {/* Search Form */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Search Properties</CardTitle>
            <CardDescription>
              Enter a location to search for available properties
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSearch} className="space-y-6">
              <div className="flex gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <Input
                      type="text"
                      placeholder="Enter address, city, or ZIP code"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Button type="submit" disabled={isSearching}>
                  {isSearching ? (
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <Search className="w-4 h-4 mr-2" />
                  )}
                  Search
                </Button>
              </div>

              {/* Filters */}
              <div className="border-t pt-6">
                <div className="flex items-center mb-4">
                  <Filter className="w-5 h-5 mr-2 text-gray-600" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    Filters
                  </h3>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Min Lot Size (sq ft)
                    </label>
                    <Input
                      type="number"
                      placeholder="20,000"
                      value={filters.minLotSize}
                      onChange={(e) => setFilters({...filters, minLotSize: e.target.value})}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Max Lot Size (sq ft)
                    </label>
                    <Input
                      type="number"
                      placeholder="40,000"
                      value={filters.maxLotSize}
                      onChange={(e) => setFilters({...filters, maxLotSize: e.target.value})}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Min Price
                    </label>
                    <Input
                      type="number"
                      placeholder="500,000"
                      value={filters.minPrice}
                      onChange={(e) => setFilters({...filters, minPrice: e.target.value})}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Max Price
                    </label>
                    <Input
                      type="number"
                      placeholder="2,000,000"
                      value={filters.maxPrice}
                      onChange={(e) => setFilters({...filters, maxPrice: e.target.value})}
                    />
                  </div>
                </div>
                <div className="mt-4 flex items-center">
                  <input
                    id="qct-dda"
                    type="checkbox"
                    checked={filters.requiresQctDda}
                    onChange={(e) => setFilters({...filters, requiresQctDda: e.target.checked})}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="qct-dda" className="ml-2 block text-sm text-gray-900 dark:text-gray-300">
                    Require QCT/DDA status
                  </label>
                </div>
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Search Results */}
        {searchResults.length > 0 && (
          <div>
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                Search Results ({searchResults.length})
              </h2>
              <Button variant="outline">
                <MapPin className="w-4 h-4 mr-2" />
                View on Map
              </Button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {searchResults.map((property) => (
                <Card key={property.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg">{property.address}</CardTitle>
                        <CardDescription>Parcel ID: {property.parcelId}</CardDescription>
                      </div>
                      <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                        property.passed 
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                          : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                      }`}>
                        {property.passed ? 'Qualified' : 'Not Qualified'}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                          <Ruler className="w-4 h-4 mr-1" />
                          Lot Size
                        </div>
                        <span className="font-medium">{formatNumber(property.lotSize)} sq ft</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                          <DollarSign className="w-4 h-4 mr-1" />
                          Assessed Value
                        </div>
                        <span className="font-medium">{formatCurrency(property.assessedValue)}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                          <Building className="w-4 h-4 mr-1" />
                          Zoning
                        </div>
                        <span className="font-medium">{property.zoning}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                          <Calendar className="w-4 h-4 mr-1" />
                          Score
                        </div>
                        <span className="font-medium">{property.evaluationScore}/100</span>
                      </div>
                    </div>
                    <div className="mt-4 flex gap-2">
                      <Button size="sm" className="flex-1">
                        View Details
                      </Button>
                      <Button size="sm" variant="outline">
                        Generate Report
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Empty State */}
        {searchResults.length === 0 && !isSearching && (
          <Card>
            <CardContent className="text-center py-12">
              <Search className="w-16 h-16 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No properties found
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Enter a location above to start searching for properties
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
